# Before (synchronous iterator):
def streaming_view(request):
    def stream_response():
        for i in range(10):
            yield f"data: {i}\n\n"
            time.sleep(0.1)
    return StreamingHttpResponse(stream_response())

# After (asynchronous iterator):
async def streaming_view(request):
    async def stream_response():
        for i in range(10):
            yield f"data: {i}\n\n"
            await asyncio.sleep(0.1)
    return StreamingHttpResponse(stream_response(), content_type='text/event-stream')