## create python virtual env
$ conda create -n microservices python=3.12  # 3.12.9

## clone git project
$ cd /home/<USER>/Projects
$ git clone ssh://*******************:60022/garderobo/client-server.git

## cd project directory, assume default name: "microservices"
$ cd /home/<USER>/Projects/microservices

## activate 'microservices' environment
$ conda activate microservices


##[optional] add alias to activate and cd
## ensure activation script path exists
$ mkdir -p ${CONDA_PREFIX}/etc/conda/activate.d
## create script for 'cd microservices'
$ echo 'cd /home/<USER>/Projects/microservices' > ${CONDA_PREFIX}/etc/conda/activate.d/cd_to_working_directory.sh

##[optional] gedit ~/.bashrc, add alias
alias wmi='conda activate microservices;

## example of alias usage (when . ~/.bashrc is applied)
$ wmi
(microservices) odyssey@Itaka:~/Projects/microservices$ 



## Note: working branch: test
$ git checkout test
Эта ветка соответствует «origin/test».
(microservices) odyssey@Itaka:~/Projects/microservices$ git branch
  master
* test

