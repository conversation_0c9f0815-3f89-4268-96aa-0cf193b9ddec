from django.core.management.base import BaseCommand, CommandError
from django.db import transaction

from services.classifier.classifier import ProductClassifier
from data.models import Product


class Command(BaseCommand):
    help = 'Classify products using LLM'

    def add_arguments(self, parser):
        parser.add_argument(
            '--product-id',
            type=int,
            help='Classify specific product by ID',
        )
        parser.add_argument(
            '--batch-size',
            type=int,
            default=10,
            help='Number of products to process in batch mode (default: 10)',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be processed without actually doing it',
        )

    def handle(self, *args, **options):
        classifier = ProductClassifier()
        
        if options['product_id']:
            self.classify_single_product(classifier, options['product_id'], options['dry_run'])
        else:
            self.classify_batch(classifier, options['batch_size'], options['dry_run'])

    def classify_single_product(self, classifier, product_id, dry_run):
        """Classify a single product by ID"""
        try:
            product = Product.objects.get(id=product_id, is_deleted=False)
        except Product.DoesNotExist:
            raise CommandError(f'Product with ID {product_id} not found')

        self.stdout.write(f'Processing product {product.id}: {product.name}')
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN - no changes will be made'))
            image_url = classifier._extract_image_url(product)
            if image_url:
                self.stdout.write(f'Would process image: {image_url}')
            else:
                self.stdout.write(self.style.ERROR('No image URL found'))
            return

        success = classifier.classify_product(product)
        if success:
            self.stdout.write(
                self.style.SUCCESS(f'Successfully classified product {product.id}')
            )
        else:
            self.stdout.write(
                self.style.ERROR(f'Failed to classify product {product.id}')
            )

    def classify_batch(self, classifier, batch_size, dry_run):
        """Classify a batch of products"""
        products_to_classify = Product.objects.filter(
            predicted_desc__isnull=True,
            is_deleted=False
        )[:batch_size]

        if not products_to_classify:
            self.stdout.write(self.style.WARNING('No products to classify'))
            return

        self.stdout.write(f'Found {len(products_to_classify)} products to classify')
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN - no changes will be made'))
            for product in products_to_classify:
                self.stdout.write(f'Would process: {product.id} - {product.name}')
                image_url = classifier._extract_image_url(product)
                if image_url:
                    self.stdout.write(f'  Image URL: {image_url}')
                else:
                    self.stdout.write(self.style.ERROR('  No image URL found'))
            return

        success_count = 0
        for i, product in enumerate(products_to_classify, 1):
            self.stdout.write(f'Processing {i}/{len(products_to_classify)}: {product.id} - {product.name}')
            
            if classifier.classify_product(product):
                success_count += 1
                self.stdout.write(self.style.SUCCESS(f'  ✓ Success'))
            else:
                self.stdout.write(self.style.ERROR(f'  ✗ Failed'))

        self.stdout.write(
            self.style.SUCCESS(
                f'Batch processing completed: {success_count}/{len(products_to_classify)} successful'
            )
        )
