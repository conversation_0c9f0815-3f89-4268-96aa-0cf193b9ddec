from typing import Optional, Dict
from django.db import transaction
from django.utils import timezone

from data.models import Product, Category, Subcategory, Color
from .llm_client import MistralLLMClient


class ProductClassifier:
    """Сервис для классификации товаров с помощью LLM"""
    
    def __init__(self):
        self.llm_client = MistralLLMClient()
    
    def get_next_product_to_classify(self) -> Optional[Product]:
        """
        Получает первый товар без заполненного predicted_desc
        
        Returns:
            Product или None если таких товаров нет
        """
        return Product.objects.filter(
            predicted_desc__isnull=True,
            is_deleted=False
        ).first()
    
    def classify_product(self, product: Product) -> bool:
        """
        Классифицирует товар и сохраняет результаты в базу
        
        Args:
            product: Товар для классификации
            
        Returns:
            True если классификация прошла успешно, False иначе
        """
        # Получаем URL изображения из данных товара
        image_url = self._extract_image_url(product)
        if not image_url:
            print(f"No image URL found for product {product.id}")
            return False
        
        # Классифицируем товар с помощью LLM
        classification_result = self.llm_client.classify_product(image_url)
        if not classification_result:
            print(f"Failed to classify product {product.id}")
            return False
        
        # Сохраняем результаты в базу
        try:
            with transaction.atomic():
                self._save_classification_results(product, classification_result)
            print(f"Successfully classified product {product.id}")
            return True
        except Exception as e:
            print(f"Error saving classification results for product {product.id}: {e}")
            return False
    
    def _extract_image_url(self, product: Product) -> Optional[str]:
        """
        Извлекает URL изображения из данных товара
        
        Args:
            product: Товар
            
        Returns:
            URL изображения или None
        """
        data = product.data or {}
        
        # Пробуем разные варианты полей с изображением
        image_fields = ['picture_url', 'image_url', 'main_image', 'primary_image']
        
        for field in image_fields:
            if field in data and data[field]:
                return data[field]
        
        # Проверяем массив pictures
        pictures = data.get('pictures', [])
        if pictures and isinstance(pictures, list):
            for picture in pictures:
                if isinstance(picture, dict):
                    # Ищем primary изображение
                    if picture.get('is_primary') and picture.get('url'):
                        return picture['url']
                    # Или просто первое доступное
                    if picture.get('url'):
                        return picture['url']
        
        return None
    
    def _save_classification_results(self, product: Product, classification: Dict):
        """
        Сохраняет результаты классификации в базу данных
        
        Args:
            product: Товар
            classification: Результаты классификации
        """
        # Получаем или создаем категорию
        category = self._get_or_create_category(classification['category'])
        
        # Получаем или создаем подкатегорию
        subcategory = self._get_or_create_subcategory(
            classification['subcategory'], 
            category
        )
        
        # Получаем или создаем цвет
        color = self._get_or_create_color(classification['color'])
        
        # Обновляем товар
        product.predicted_category = category
        product.predicted_subcategory = subcategory
        product.predicted_color = color
        product.predicted_desc = classification['description']
        product.dt_updated = timezone.now()
        
        product.save(update_fields=[
            'predicted_category',
            'predicted_subcategory', 
            'predicted_color',
            'predicted_desc',
            'dt_updated'
        ])
    
    def _get_or_create_category(self, name: str) -> Category:
        """Получает или создает категорию"""
        category, created = Category.objects.get_or_create(
            name=name,
            defaults={'is_deleted': False}
        )
        if created:
            print(f"Created new category: {name}")
        return category
    
    def _get_or_create_subcategory(self, name: str, category: Category) -> Subcategory:
        """Получает или создает подкатегорию"""
        subcategory, created = Subcategory.objects.get_or_create(
            name=name,
            category=category,
            defaults={'is_deleted': False}
        )
        if created:
            print(f"Created new subcategory: {category.name} > {name}")
        return subcategory
    
    def _get_or_create_color(self, name: str) -> Color:
        """Получает или создает цвет"""
        color, created = Color.objects.get_or_create(
            name=name,
            defaults={'is_deleted': False}
        )
        if created:
            print(f"Created new color: {name}")
        return color
    
    def process_next_product(self) -> bool:
        """
        Обрабатывает следующий товар в очереди
        
        Returns:
            True если товар был обработан, False если товаров для обработки нет
        """
        product = self.get_next_product_to_classify()
        if not product:
            print("No products to classify")
            return False
        
        print(f"Processing product {product.id}: {product.name}")
        return self.classify_product(product)
