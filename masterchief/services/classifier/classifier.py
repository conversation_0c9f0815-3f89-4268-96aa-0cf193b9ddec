from typing import Optional, Dict
from django.db import transaction
from django.utils import timezone

from data.models import Product, Category, Subcategory, Color
from .llm_client import MistralLLMClient


class ProductClassifier:
    """Сервис для классификации товаров с помощью LLM"""

    def __init__(self):
        self.llm_client = MistralLLMClient()

    def get_next_product_to_classify(self) -> Optional[Product]:
        """
        Получает первый товар без заполненной predicted_category

        Returns:
            Product или None если таких товаров нет
        """
        return Product.objects.filter(
            predicted_category__isnull=True,
            is_deleted=False
        ).first()

    def get_next_product_for_details(self) -> Optional[Product]:
        """
        Получает первый товар с категорией, но без заполненного predicted_desc

        Returns:
            Product или None если таких товаров нет
        """
        return Product.objects.filter(
            predicted_category__isnull=False,
            predicted_desc__isnull=True,
            is_deleted=False
        ).first()

    def classify_product(self, product: Product) -> bool:
        """
        Классифицирует товар полностью (двухэтапная классификация)

        Args:
            product: Товар для классификации

        Returns:
            True если классификация прошла успешно, False иначе
        """
        # Этап 1: Определяем категорию, если её нет
        if not product.predicted_category:
            if not self.classify_product_category(product):
                return False
            # Обновляем объект из базы данных
            product.refresh_from_db()

        # Этап 2: Определяем детали, если их нет
        if not product.predicted_desc:
            return self.classify_product_details(product)

        print(f"Product {product.id} is already fully classified")
        return True

    def classify_product_category(self, product: Product) -> bool:
        """
        Определяет родительскую категорию товара (этап 1)

        Args:
            product: Товар для классификации

        Returns:
            True если классификация прошла успешно, False иначе
        """
        # Получаем URL изображения из данных товара
        image_url = self._extract_image_url(product)
        if not image_url:
            print(f"No image URL found for product {product.id}")
            return False

        # Получаем список всех доступных категорий
        categories = list(Category.objects.filter(is_deleted=False).values_list('name', flat=True))
        if not categories:
            print("No categories found in database")
            return False

        # Классифицируем категорию с помощью LLM
        category_name = self.llm_client.classify_product_category(product.name, image_url, categories)
        if not category_name:
            print(f"Failed to classify category for product {product.id}")
            return False

        try:
            with transaction.atomic():
                # Получаем или создаем категорию
                category = self._get_or_create_category(category_name)

                # Обновляем товар
                product.predicted_category = category
                product.save(update_fields=['predicted_category'])

                print(f"Successfully classified category for product {product.id}: {category_name}")
                return True
        except Exception as e:
            print(f"Error saving category for product {product.id}: {e}")
            return False

    def classify_product_details(self, product: Product) -> bool:
        """
        Определяет подкатегорию, цвет и описание товара (этап 2)

        Args:
            product: Товар для классификации (должен иметь predicted_category)

        Returns:
            True если классификация прошла успешно, False иначе
        """
        if not product.predicted_category:
            print(f"Product {product.id} has no predicted_category")
            return False

        # Получаем URL изображения из данных товара
        image_url = self._extract_image_url(product)
        if not image_url:
            print(f"No image URL found for product {product.id}")
            return False

        # Получаем список подкатегорий для данной категории
        subcategories = list(
            product.predicted_category.subcategories
            .filter(is_deleted=False)
            .values('name', 'description')
        )

        if not subcategories:
            print(f"No subcategories found for category {product.predicted_category.name}")
            return False

        # Классифицируем детали с помощью LLM
        details_result = self.llm_client.classify_product_details(
            image_url, product.name, subcategories
        )
        if not details_result:
            print(f"Failed to classify details for product {product.id}")
            return False

        try:
            with transaction.atomic():
                self._save_details_results(product, details_result)
                print(f"Successfully classified details for product {product.id}: {product.name}")
                return True
        except Exception as e:
            print(f"Error saving details for product {product.id}: {e}")
            return False

    def _extract_image_url(self, product: Product) -> Optional[str]:
        """
        Извлекает URL изображения из данных товара

        Args:
            product: Товар

        Returns:
            URL изображения или None
        """
        data = product.data or {}

        # Пробуем разные варианты полей с изображением
        image_fields = ['picture_url', 'image_url', 'main_image', 'primary_image']

        for field in image_fields:
            if field in data and data[field]:
                return data[field]

        # Проверяем массив pictures
        pictures = data.get('pictures', [])
        if pictures and isinstance(pictures, list):
            for picture in pictures:
                if isinstance(picture, dict):
                    # Ищем primary изображение
                    if picture.get('is_primary') and picture.get('url'):
                        return picture['url']
                    # Или просто первое доступное
                    if picture.get('url'):
                        return picture['url']

        return None

    def _save_classification_results(self, product: Product, classification: Dict):
        """
        Сохраняет результаты классификации в базу данных

        Args:
            product: Товар
            classification: Результаты классификации
        """
        # Получаем или создаем категорию
        category = self._get_or_create_category(classification['category'])

        # Получаем или создаем подкатегорию
        subcategory = self._get_or_create_subcategory(
            classification['subcategory'],
            category
        )

        # Получаем или создаем цвет
        color = self._get_or_create_color(classification['color'])

        # Обновляем товар
        product.predicted_category = category
        product.predicted_subcategory = subcategory
        product.predicted_color = color
        product.predicted_desc = classification['description']
        product.dt_updated = timezone.now()

        product.save(update_fields=[
            'predicted_category',
            'predicted_subcategory',
            'predicted_color',
            'predicted_desc',
            'dt_updated'
        ])

    def _save_details_results(self, product: Product, details: Dict):
        """
        Сохраняет результаты детальной классификации в базу данных

        Args:
            product: Товар (должен иметь predicted_category)
            details: Результаты детальной классификации
        """
        # Получаем или создаем подкатегорию
        subcategory = self._get_or_create_subcategory(
            details['subcategory'],
            product.predicted_category
        )

        # Получаем или создаем цвет
        color = self._get_or_create_color(details['color'])

        # Обновляем товар
        product.predicted_subcategory = subcategory
        product.predicted_color = color
        product.predicted_desc = details['description']
        product.dt_updated = timezone.now()

        product.save(update_fields=[
            'predicted_subcategory',
            'predicted_color',
            'predicted_desc',
            'dt_updated'
        ])

    def _get_or_create_category(self, name: str) -> Category:
        """Получает или создает категорию"""
        category, created = Category.objects.get_or_create(
            name=name,
            defaults={'is_deleted': False}
        )
        if created:
            print(f"Created new category: {name}")
        return category

    def _get_or_create_subcategory(self, name: str, category: Category) -> Subcategory:
        """Получает или создает подкатегорию"""
        subcategory, created = Subcategory.objects.get_or_create(
            name=name,
            category=category,
            defaults={'is_deleted': False}
        )
        if created:
            print(f"Created new subcategory: {category.name} > {name}")
        return subcategory

    def _get_or_create_color(self, name: str) -> Color:
        """Получает или создает цвет"""
        color, created = Color.objects.get_or_create(
            name=name,
            defaults={'is_deleted': False}
        )
        if created:
            print(f"Created new color: {name}")
        return color

    def process_next_product(self) -> bool:
        """
        Обрабатывает следующий товар в очереди (двухэтапная классификация)

        Returns:
            True если товар был обработан, False если товаров для обработки нет
        """
        # Сначала пробуем найти товар без категории (этап 1)
        product = self.get_next_product_to_classify()
        if product:
            print(f"Processing product category {product.id}: {product.name}")
            return self.classify_product_category(product)

        # Если товаров без категории нет, ищем товары без деталей (этап 2)
        product = self.get_next_product_for_details()
        if product:
            print(f"Processing product details {product.id}: {product.name}")
            return self.classify_product_details(product)

        print("No products to classify")
        return False
