from celery import shared_task
from django.utils import timezone

from common import logger
from masterchief.tasks.bases import Task
from .classifier import ProductClassifier


@shared_task(base=Task, bind=True)
def task_classify_next_product(self, *args, **kwargs):
    """
    Celery задача для классификации следующего товара в очереди
    """
    logger.info('Task classify_next_product started')
    
    try:
        classifier = ProductClassifier()
        success = classifier.process_next_product()
        
        if success:
            logger.info('Product classification completed successfully')
        else:
            logger.info('No products to classify or classification failed')
            
        return {'success': success, 'timestamp': timezone.now().isoformat()}
        
    except Exception as e:
        logger.error(f'Error in classify_next_product task: {e}')
        raise


@shared_task(base=Task, bind=True)
def task_classify_product_by_id(self, product_id: int, *args, **kwargs):
    """
    Celery задача для классификации конкретного товара по ID
    
    Args:
        product_id: ID товара для классификации
    """
    logger.info(f'Task classify_product_by_id started for product {product_id}')
    
    try:
        from data.models import Product
        
        try:
            product = Product.objects.get(id=product_id, is_deleted=False)
        except Product.DoesNotExist:
            logger.error(f'Product {product_id} not found')
            return {'success': False, 'error': 'Product not found'}
        
        classifier = ProductClassifier()
        success = classifier.classify_product(product)
        
        if success:
            logger.info(f'Product {product_id} classification completed successfully')
        else:
            logger.error(f'Product {product_id} classification failed')
            
        return {
            'success': success, 
            'product_id': product_id,
            'timestamp': timezone.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f'Error in classify_product_by_id task: {e}')
        raise


@shared_task(base=Task, bind=True)
def task_classify_batch_products(self, batch_size: int = 10, *args, **kwargs):
    """
    Celery задача для классификации пакета товаров
    
    Args:
        batch_size: Количество товаров для обработки в одном пакете
    """
    logger.info(f'Task classify_batch_products started with batch_size={batch_size}')
    
    try:
        classifier = ProductClassifier()
        processed_count = 0
        success_count = 0
        
        for i in range(batch_size):
            product = classifier.get_next_product_to_classify()
            if not product:
                logger.info(f'No more products to classify. Processed {processed_count} products.')
                break
                
            processed_count += 1
            logger.info(f'Processing product {product.id} ({processed_count}/{batch_size})')
            
            if classifier.classify_product(product):
                success_count += 1
            else:
                logger.error(f'Failed to classify product {product.id}')
        
        logger.info(f'Batch classification completed: {success_count}/{processed_count} successful')
        
        return {
            'success': True,
            'processed_count': processed_count,
            'success_count': success_count,
            'batch_size': batch_size,
            'timestamp': timezone.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f'Error in classify_batch_products task: {e}')
        raise
