from unittest.mock import patch, MagicMock
from django.test import TestCase
from django.utils import timezone

from data.models import Product, Vendor, Category, Subcategory, Color
from .classifier import ProductClassifier
from .llm_client import MistralLLMClient


class ProductClassifierTestCase(TestCase):
    def setUp(self):
        # Создаем тестовые данные
        self.vendor = Vendor.objects.create(name="Test Vendor")
        self.product = Product.objects.create(
            name="Test Product",
            vendor=self.vendor,
            data={
                "picture_url": "https://example.com/image.jpg",
                "pictures": [
                    {"url": "https://example.com/image1.jpg", "is_primary": True},
                    {"url": "https://example.com/image2.jpg", "is_primary": False}
                ]
            }
        )

    def test_get_next_product_to_classify(self):
        """Тест получения следующего товара для классификации категории"""
        classifier = ProductClassifier()

        # Товар без predicted_category должен быть найден
        product = classifier.get_next_product_to_classify()
        self.assertEqual(product.id, self.product.id)

        # После заполнения predicted_category товар не должен возвращаться
        category = Category.objects.create(name="Test Category")
        self.product.predicted_category = category
        self.product.save()

        product = classifier.get_next_product_to_classify()
        self.assertIsNone(product)

    def test_get_next_product_for_details(self):
        """Тест получения следующего товара для классификации деталей"""
        classifier = ProductClassifier()

        # Товар без категории не должен возвращаться
        product = classifier.get_next_product_for_details()
        self.assertIsNone(product)

        # Добавляем категорию
        category = Category.objects.create(name="Test Category")
        self.product.predicted_category = category
        self.product.save()

        # Теперь товар должен быть найден
        product = classifier.get_next_product_for_details()
        self.assertEqual(product.id, self.product.id)

        # После заполнения predicted_desc товар не должен возвращаться
        self.product.predicted_desc = "Test description"
        self.product.save()

        product = classifier.get_next_product_for_details()
        self.assertIsNone(product)

    def test_extract_image_url(self):
        """Тест извлечения URL изображения"""
        classifier = ProductClassifier()

        # Тест с picture_url
        url = classifier._extract_image_url(self.product)
        self.assertEqual(url, "https://example.com/image.jpg")

        # Тест с pictures array
        self.product.data = {
            "pictures": [
                {"url": "https://example.com/image1.jpg", "is_primary": True}
            ]
        }
        url = classifier._extract_image_url(self.product)
        self.assertEqual(url, "https://example.com/image1.jpg")

        # Тест без изображений
        self.product.data = {}
        url = classifier._extract_image_url(self.product)
        self.assertIsNone(url)

    @patch('services.classifier.classifier.MistralLLMClient')
    def test_classify_product_success(self, mock_llm_client):
        """Тест успешной классификации товара"""
        # Настраиваем мок
        mock_client = MagicMock()
        mock_llm_client.return_value = mock_client
        mock_client.classify_product.return_value = {
            'category': 'Одежда',
            'subcategory': 'Куртки',
            'color': 'Черный',
            'description': 'Стильная черная куртка'
        }

        classifier = ProductClassifier()
        success = classifier.classify_product(self.product)

        self.assertTrue(success)

        # Проверяем, что товар обновился
        self.product.refresh_from_db()
        self.assertIsNotNone(self.product.predicted_category)
        self.assertIsNotNone(self.product.predicted_subcategory)
        self.assertIsNotNone(self.product.predicted_color)
        self.assertEqual(self.product.predicted_desc, 'Стильная черная куртка')

        # Проверяем, что созданы связанные объекты
        self.assertTrue(Category.objects.filter(name='Одежда').exists())
        self.assertTrue(Subcategory.objects.filter(name='Куртки').exists())
        self.assertTrue(Color.objects.filter(name='Черный').exists())

    @patch('services.classifier.classifier.MistralLLMClient')
    def test_classify_product_no_image(self, mock_llm_client):
        """Тест классификации товара без изображения"""
        # Убираем изображение
        self.product.data = {}
        self.product.save()

        classifier = ProductClassifier()
        success = classifier.classify_product(self.product)

        self.assertFalse(success)

        # Проверяем, что товар не изменился
        self.product.refresh_from_db()
        self.assertIsNone(self.product.predicted_category)

    @patch('services.classifier.classifier.MistralLLMClient')
    def test_classify_product_llm_failure(self, mock_llm_client):
        """Тест обработки ошибки LLM"""
        # Настраиваем мок на возврат None (ошибка)
        mock_client = MagicMock()
        mock_llm_client.return_value = mock_client
        mock_client.classify_product.return_value = None

        classifier = ProductClassifier()
        success = classifier.classify_product(self.product)

        self.assertFalse(success)

        # Проверяем, что товар не изменился
        self.product.refresh_from_db()
        self.assertIsNone(self.product.predicted_category)


class MistralLLMClientTestCase(TestCase):
    @patch.dict('os.environ', {'MISTRAL_API_KEY': 'test_key'})
    def test_init_with_api_key(self):
        """Тест инициализации с API ключом"""
        client = MistralLLMClient()
        self.assertEqual(client.api_key, 'test_key')

    def test_init_without_api_key(self):
        """Тест инициализации без API ключа"""
        with patch.dict('os.environ', {}, clear=True):
            with self.assertRaises(ValueError):
                MistralLLMClient()

    @patch.dict('os.environ', {'MISTRAL_API_KEY': 'test_key'})
    def test_parse_response_success(self):
        """Тест парсинга успешного ответа"""
        client = MistralLLMClient()

        response = {
            'choices': [{
                'message': {
                    'content': '''Вот анализ изображения:
                    {
                        "category": "Одежда",
                        "subcategory": "Куртки",
                        "color": "Черный",
                        "description": "Стильная куртка"
                    }
                    '''
                }
            }]
        }

        result = client._parse_response(response)

        self.assertIsNotNone(result)
        self.assertEqual(result['category'], 'Одежда')
        self.assertEqual(result['subcategory'], 'Куртки')
        self.assertEqual(result['color'], 'Черный')
        self.assertEqual(result['description'], 'Стильная куртка')

    @patch.dict('os.environ', {'MISTRAL_API_KEY': 'test_key'})
    def test_parse_response_invalid_json(self):
        """Тест парсинга ответа с невалидным JSON"""
        client = MistralLLMClient()

        response = {
            'choices': [{
                'message': {
                    'content': 'Это не JSON ответ'
                }
            }]
        }

        result = client._parse_response(response)
        self.assertIsNone(result)
