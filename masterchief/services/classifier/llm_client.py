import os
import json
import requests
from typing import Dict, Optional
from django.conf import settings


class MistralLLMClient:
    """Клиент для работы с Mistral LLM API"""

    def __init__(self):
        self.api_key = os.getenv('MISTRAL_API_KEY')
        self.base_url = "https://api.mistral.ai/v1"
        self.model = "mistral-medium-latest"

        if not self.api_key:
            raise ValueError("MISTRAL_API_KEY environment variable is required")

    def classify_product_category(self, product_name: str, categories: list) -> Optional[str]:
        """
        Определяет родительскую категорию товара по названию

        Args:
            product_name: Название товара
            categories: Список доступных категорий

        Returns:
            Название категории из предоставленного списка
        """
        prompt = self._build_category_classification_prompt(categories)

        try:
            response = self._make_text_api_request(prompt, product_name)
            return self._parse_category_response(response, categories)
        except Exception as e:
            print(f"Error classifying product category: {e}")
            return None

    def classify_product_details(self, image_url: str, product_name: str, subcategories: list) -> Optional[Dict]:
        """
        Классифицирует товар по изображению для определения подкатегории, цвета и описания

        Args:
            image_url: URL изображения товара
            product_name: Название товара
            subcategories: Список подкатегорий с описаниями

        Returns:
            Dict с полями: subcategory, color, description
        """
        prompt = self._build_details_classification_prompt(subcategories)

        try:
            response = self._make_api_request(prompt, image_url, product_name)
            return self._parse_details_response(response)
        except Exception as e:
            print(f"Error classifying product details: {e}")
            return None

    def _build_classification_prompt(self) -> str:
        """Создает промпт для классификации товара"""
        return """
Проанализируй изображение товара и определи следующие характеристики:

1. category - основная категория товара (например: "Одежда", "Обувь", "Аксессуары")
2. subcategory - подкатегория (например: "Куртки", "Джинсы", "Кроссовки")
3. color - основной цвет товара (например: "Черный", "Синий", "Красный")
4. description - подробное описание товара (2-3 предложения)

Ответь в формате JSON:
{
    "category": "категория",
    "subcategory": "подкатегория",
    "color": "цвет",
    "description": "описание товара"
}

Используй только русский язык для всех полей.
"""

    def _make_api_request(self, prompt: str, image_url: str) -> Dict:
        """Выполняет запрос к Mistral API"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": prompt
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": image_url
                            }
                        }
                    ]
                }
            ],
            "max_tokens": 500,
            "temperature": 0.1
        }

        response = requests.post(
            f"{self.base_url}/chat/completions",
            headers=headers,
            json=payload,
            timeout=30
        )

        response.raise_for_status()
        return response.json()

    def _parse_response(self, response: Dict) -> Optional[Dict]:
        """Парсит ответ от API и извлекает JSON с классификацией"""
        try:
            content = response['choices'][0]['message']['content']

            # Ищем JSON в ответе
            start_idx = content.find('{')
            end_idx = content.rfind('}') + 1

            if start_idx == -1 or end_idx == 0:
                print(f"No JSON found in response: {content}")
                return None

            json_str = content[start_idx:end_idx]
            result = json.loads(json_str)

            # Проверяем наличие всех необходимых полей
            required_fields = ['category', 'subcategory', 'color', 'description']
            if not all(field in result for field in required_fields):
                print(f"Missing required fields in response: {result}")
                return None

            return result

        except (KeyError, json.JSONDecodeError, IndexError) as e:
            print(f"Error parsing response: {e}")
            return None
