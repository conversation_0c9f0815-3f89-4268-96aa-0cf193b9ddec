import os
import json
import requests
import time
from typing import Dict, Optional
from django.conf import settings


class MistralLLMClient:
    """Клиент для работы с Mistral LLM API"""

    def __init__(self):
        self.api_key = os.getenv('MISTRAL_API_KEY')
        self.base_url = "https://api.mistral.ai/v1"
        self.model = "mistral-medium-latest"

        if not self.api_key:
            raise ValueError("MISTRAL_API_KEY environment variable is required")

    def classify_product_category(self, product_name: str, categories: list) -> Optional[str]:
        """
        Определяет родительскую категорию товара по названию

        Args:
            product_name: Название товара
            categories: Список доступных категорий

        Returns:
            Название категории из предоставленного списка
        """
        prompt = self._build_category_classification_prompt(categories)

        try:
            response = self._make_text_api_request(prompt, product_name)
            return self._parse_category_response(response, categories)
        except Exception as e:
            print(f"Error classifying product category: {e}")
            return None

    def classify_product_details(self, image_url: str, product_name: str, subcategories: list) -> Optional[Dict]:
        """
        Классифицирует товар по изображению для определения подкатегории, цвета и описания

        Args:
            image_url: URL изображения товара
            product_name: Название товара
            subcategories: Список подкатегорий с описаниями

        Returns:
            Dict с полями: subcategory, color, description
        """
        prompt = self._build_details_classification_prompt(subcategories)

        try:
            response = self._make_api_request(prompt, image_url, product_name)
            return self._parse_details_response(response)
        except Exception as e:
            print(f"Error classifying product details: {e}")
            return None

    def _build_category_classification_prompt(self, categories: list) -> str:
        """Создает промпт для определения категории товара"""
        categories_list = "\n".join([f"- {cat}" for cat in categories])

        return f"""
Определи родительскую категорию товара по его названию.

Доступные категории:
{categories_list}

Ответь ТОЛЬКО названием категории из предоставленного списка. Не добавляй никаких дополнительных слов или объяснений.

Название товара: {{product_name}}
"""

    def _build_details_classification_prompt(self, subcategories: list) -> str:
        """Создает промпт для детальной классификации товара"""
        subcategories_info = []
        for subcat in subcategories:
            name = subcat.get('name', '')
            description = subcat.get('description', '')
            if description:
                subcategories_info.append(f"- {name}: {description}")
            else:
                subcategories_info.append(f"- {name}")

        subcategories_text = "\n".join(subcategories_info)

        return f"""
Проанализируй изображение товара и его название, затем определи следующие характеристики:

1. subcategory - подкатегория из предоставленного списка
2. color - основной цвет товара (например: "Черный", "Синий", "Красный")
3. description - подробное описание товара (2-3 предложения)

Доступные подкатегории:
{subcategories_text}

Ответь в формате JSON:
{{
    "subcategory": "подкатегория из списка",
    "color": "цвет",
    "description": "описание товара"
}}

Используй только русский язык для всех полей.
Название товара: {{product_name}}
"""

    def _make_text_api_request(self, prompt: str, product_name: str) -> Dict:
        """Выполняет текстовый запрос к Mistral API"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        formatted_prompt = prompt.format(product_name=product_name)

        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "user",
                    "content": formatted_prompt
                }
            ],
            "max_tokens": 100,
            "temperature": 0.1
        }

        response = requests.post(
            f"{self.base_url}/chat/completions",
            headers=headers,
            json=payload,
            timeout=30
        )

        response.raise_for_status()
        return response.json()

    def _make_api_request(self, prompt: str, image_url: str, product_name: str = None) -> Dict:
        """Выполняет запрос к Mistral API"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        # Форматируем промпт с названием товара если оно предоставлено
        formatted_prompt = prompt.format(product_name=product_name) if product_name else prompt

        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": formatted_prompt
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": image_url
                            }
                        }
                    ]
                }
            ],
            "max_tokens": 500,
            "temperature": 0.1
        }

        response = requests.post(
            f"{self.base_url}/chat/completions",
            headers=headers,
            json=payload,
            timeout=30
        )

        response.raise_for_status()
        return response.json()

    def _parse_category_response(self, response: Dict, categories: list) -> Optional[str]:
        """Парсит ответ от API для определения категории"""
        try:
            content = response['choices'][0]['message']['content'].strip()

            # Ищем точное совпадение с одной из категорий
            for category in categories:
                if category.lower() in content.lower() or content.lower() in category.lower():
                    return category

            # Если точного совпадения нет, возвращаем первую найденную категорию
            for category in categories:
                if any(word in content.lower() for word in category.lower().split()):
                    return category

            print(f"No matching category found in response: {content}")
            return None

        except (KeyError, IndexError, json.JSONDecodeError) as e:
            print(f"Error parsing category response: {e}")
            return None

    def _parse_details_response(self, response: Dict) -> Optional[Dict]:
        """Парсит ответ от API для детальной классификации"""
        try:
            content = response['choices'][0]['message']['content']

            # Ищем JSON в ответе
            start_idx = content.find('{')
            end_idx = content.rfind('}') + 1

            if start_idx == -1 or end_idx == 0:
                print(f"No JSON found in response: {content}")
                return None

            json_str = content[start_idx:end_idx]
            result = json.loads(json_str)

            # Проверяем наличие всех необходимых полей
            required_fields = ['subcategory', 'color', 'description']
            if not all(field in result for field in required_fields):
                print(f"Missing required fields in response: {result}")
                return None

            return result

        except (KeyError, IndexError, json.JSONDecodeError) as e:
            print(f"Error parsing details response: {e}")
            return None


