# Services

Этот пакет содержит различные сервисы для обработки данных.

## Classifier Service

Сервис для автоматической классификации товаров с помощью LLM (Mistral).

### Функциональность

1. **Поиск товаров для классификации** - находит товары без заполненного поля `predicted_desc`
2. **Классификация с помощью LLM** - отправляет изображение товара в Mistral API для получения:
   - Категории
   - Подкатегории  
   - Цвета
   - Описания
3. **Сохранение результатов** - создает записи в таблицах Category, Subcategory, Color и обновляет товар

### Настройка

1. Добавьте в `.env` файл:
```
MISTRAL_API_KEY=your_mistral_api_key_here
```

2. Выполните миграции для создания новых таблиц:
```bash
python manage.py makemigrations data
python manage.py migrate
```

### Использование

#### Management команда

```bash
# Классифицировать один товар по ID
python manage.py classify_products --product-id 123

# Классифицировать пакет товаров (по умолчанию 10)
python manage.py classify_products --batch-size 5

# Посмотреть что будет обработано без выполнения
python manage.py classify_products --dry-run --batch-size 5
```

#### Celery задачи

```python
# Классифицировать следующий товар в очереди
from services.classifier.tasks import task_classify_next_product
task_classify_next_product.delay()

# Классифицировать конкретный товар
from services.classifier.tasks import task_classify_product_by_id
task_classify_product_by_id.delay(product_id=123)

# Классифицировать пакет товаров
from services.classifier.tasks import task_classify_batch_products
task_classify_batch_products.delay(batch_size=10)
```

#### Прямое использование

```python
from services.classifier.classifier import ProductClassifier
from data.models import Product

classifier = ProductClassifier()

# Получить следующий товар для классификации
product = classifier.get_next_product_to_classify()

# Классифицировать товар
if product:
    success = classifier.classify_product(product)
    print(f"Classification {'successful' if success else 'failed'}")
```

### Автоматическая обработка

Сервис настроен на автоматическую обработку через Celery Beat:
- Каждые 5 минут обрабатывается пакет из 5 товаров
- Настройка в `masterchief/settings/celery_config.py`

### Модели данных

#### Category
- `name` - название категории
- Базовые поля: `id`, `dt_created`, `dt_updated`, `is_deleted`

#### Subcategory  
- `name` - название подкатегории
- `category` - связь с категорией
- Уникальность по паре (category, name)

#### Color
- `name` - название цвета
- `hex_code` - HEX код цвета (опционально)

#### Product (новые поля)
- `predicted_category` - ForeignKey на Category
- `predicted_subcategory` - ForeignKey на Subcategory  
- `predicted_color` - ForeignKey на Color
- `predicted_desc` - текстовое описание

### Логирование

Сервис использует стандартное Django логирование. Все операции логируются с соответствующими уровнями.

### Обработка ошибок

- Если API ключ не настроен - выбрасывается ValueError
- Если изображение не найдено - товар пропускается
- Если LLM вернул некорректный ответ - товар пропускается
- Все ошибки логируются для отладки
