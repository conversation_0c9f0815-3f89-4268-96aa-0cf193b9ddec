import logging
import random

import sentry_sdk

from masterchief.config import NODE_ENV, RELEASE, SENTRY_DSN, NODE_NAME

logger = logging.getLogger('masterchief')


SPAM_CATEGORIES = ('httplib', 'query', 'elasticapm.transport', 'redis')


def before_breadcrumb(crumb, hint):
    if crumb['category'] in SPAM_CATEGORIES:
        return None
    return crumb


if NODE_ENV != 'test':
    from sentry_sdk.integrations.django import DjangoIntegration
    from sentry_sdk.integrations.celery import CeleryIntegration

    sentry_sdk.init(
        dsn=SENTRY_DSN,
        integrations=[DjangoIntegration(), CeleryIntegration()],
        environment=NODE_ENV,
        server_name=NODE_NAME,
        release=RELEASE,
        before_breadcrumb=before_breadcrumb
    )


def capture_message(message=None, level='info', logger_instance=logger, do_log=True, exception=None,
                    sample=False, *args, **kwargs):
    if sample:
        # i.e. sample: 0.1 for [x10], 0.01 for [x100]
        if random.random() > sample:
            return
        message = (message or '') + f' [x{int(1/sample + 0.5)}]'

    log_method = getattr(logger_instance, level, logger.info)
    if exception:
        message = message or f'Got exception: {type(exception)} {exception}'
        # capture_exception(exception, logger_instance=logger_instance, *args, **kwargs)
        level = 'error'
        log_method = logger_instance.warning

    # logger.error will result in sentry api call
    if do_log:
        log_method(f'{message}, args: {args}, kwargs: {kwargs}, exception: {exception}')
        if exception:
            logger.exception(exception)

    with sentry_sdk.configure_scope() as scope:
        tags = kwargs.pop('tags', {})
        for k, v in tags.items():
            scope.set_tag(k, v)
        for k, v in kwargs.items():
            scope.set_extra(k, v)
        sentry_sdk.capture_message(message, level=level)


def capture_exception(exc, logger_instance=logger, *args, **kwargs):
    logger_instance.exception(exc)
    sentry_sdk.capture_exception(exc)
