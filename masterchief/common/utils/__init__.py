import datetime
import hashlib
import json
from itertools import islice

from django.core.cache import cache, caches
from django.utils import timezone
from django.utils.functional import cached_property  # noqa

from common.logging import capture_message
from masterchief.config import CACHE_KEY_PREFIX, DEFAULT_DATETIME_FORMAT, CURRENT_HOST, CLIENT_HOST_ADMIN_URL

try:
    filebased_cache = caches['filebased']
except:
    filebased_cache = cache

try:
    common_cache = caches['redis']  # aka common_cache for-through all client nodes
except:
    common_cache = cache
    # common_cache = filebased_cache  # aka common_cache for-through all client nodes


def confirm_input(msg, agree='yY'):
    if agree not in msg:
        msg += f' [{agree}] '
    while True:
        confirm = input(msg)
        if confirm:
            if confirm in agree:
                return True
            else:
                return False
        else:
            print('\n')


def get_current_host_url(url_path=''):
    if url_path and url_path[0] != '/':
        url_path = f'/{url_path}'
    return f'{CURRENT_HOST}{url_path}'


def get_client_host_admin_url(url_path=''):
    if url_path and url_path[0] != '/':
        url_path = f'/{url_path}'
    return f'{CLIENT_HOST_ADMIN_URL}{url_path}'


def convert_dd_to_dict(d):
    for k, v in d.items():
        if isinstance(v, dict):
            d[k] = convert_dd_to_dict(v)
    return dict(d)


# TODO: override cache.get/filebased_cache.get (and also .set methods) to use that prefix transparently?
def get_cache_key(key):
    return CACHE_KEY_PREFIX + key


def chunked(it, size):
    it = iter(it)
    return iter(lambda: tuple(islice(it, size)), ())


def chunked_queryset(queryset, chunk_size=500, id_field='id', order=None):
    """ Slice a queryset into chunks. """
    # if order == 'reverse':
    #     # should iterate and yield, then return
    #     for x in chunked_queryset_reverse(queryset, chunk_size=chunk_size, id_field=id_field):
    #         yield x
    #     return

    queryset = queryset.order_by(id_field)
    start_id = 0

    while True:
        qs = queryset.filter(**{f'{id_field}__gt': start_id})
        if not qs.exists():
            # No entry left
            break

        try:
            # Fetch chunk_size entries if possible
            end_id = qs.values_list(id_field, flat=True)[chunk_size - 1]

            # Fetch rest entries if less than chunk_size left
        except IndexError:
            end_id = queryset.values_list(id_field, flat=True).last()

        # end_id can be None here, so getting exc: "Cannot use None as a query value", TODO: check for/debug qs/tests?
        if end_id is None:
            try:
                capture_message('Queryset end_id is None', qs=str(queryset.query))  # empty qs str can raise Exc?
            except Exception as e:
                capture_message('Queryset end_id is None', exception=e)
            return

        # gt start id -> lte end id
        yield qs.filter(**{f'{id_field}__lte': end_id})

        start_id = end_id


def chunked_queryset_data(queryset, chunk_size=1000, id_field='id'):
    count = queryset.count()
    total_chunks, rest = divmod(count, chunk_size)
    if rest:
        total_chunks += 1
    return {
        'chunk_size': chunk_size,
        'count': count,
        'total_chunks': total_chunks,
        'chunks': enumerate(chunked_queryset(queryset, chunk_size=chunk_size, id_field=id_field), 1)
    }


def raw_delete_queryset(qs):
    # warning: no any checks for on_delete related objects, no signals, just raw db delete
    # used mostly to remove items product scores, actually anything, that has no other dependencies
    return qs._raw_delete(using=qs.db)


def get_hex_digest(s):
    if isinstance(s, str):
        s = s.encode('utf-8')
    return hashlib.md5(s).hexdigest()


def get_now():
    return timezone.now()


def get_utc_now():
    return datetime.datetime.now().astimezone(datetime.timezone.utc)


def seconds_passed(start, now=None):
    return ((now or datetime.datetime.now()) - start).total_seconds()


def dict_hash(d):
    """
    MD5 hash of a dictionary.
    We need to sort arguments so hash for {'a': 1, 'b': 2} is the same as for {'b': 2, 'a': 1}
    """
    return hashlib.md5(
        json.dumps(d, sort_keys=True).encode()
    ).hexdigest()


def parse_dt(dt_str, fmts=['%Y-%m-%d', '%Y-%m-%d %H:%M', DEFAULT_DATETIME_FORMAT], default=None, as_utc=False):
    if not dt_str:
        return
    dt_str = dt_str.strip()
    for fmt in fmts:
        try:
            parsed = datetime.datetime.strptime(dt_str, fmt)
            if as_utc:
                # parsed = parsed.replace(tzinfo=pytz.UTC)
                # parsed = pytz.UTC.localize(parsed)
                parsed = parsed.astimezone(datetime.timezone.utc)
            return parsed
        except:
            continue
    return default


def str_dt(dt):
    return  str(dt or '').rsplit('+', 1)[0]
