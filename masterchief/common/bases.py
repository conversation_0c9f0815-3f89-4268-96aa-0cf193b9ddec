import datetime
import os
import json
import random
import pickle
import jsonlines

from django.core.cache import caches
from pprint import pprint
from collections import defaultdict
from decimal import Decimal

from django.utils.functional import cached_property, classproperty
from django.conf import settings
from django.core.exceptions import EmptyResultSet
from django.db import models

from masterchief.config import DEFAULT_DATETIME_FORMAT, CACHE_TIMEOUT, CACHE_VERSION, CACHE_KEY_PREFIX
from .utils import seconds_passed, get_now, filebased_cache, cache, get_hex_digest
from .logging import logger

class AlertsNotifierMixin:
    pass


class LoggerMixin(AlertsNotifierMixin):
    _DEBUG = False
    _DEBUG_VARIOUS = False

    _log_now_start = None

    @classproperty
    def logger(cls):
        return logger

    def log(self, msg):
        if self._DEBUG:
            self.logger.info(msg)

    def log_bad_request(self, request, response_data=None, msg=None, x_api_key='HTTP_X_API_KEY'):
        if self._DEBUG_VARIOUS:
            response_data = response_data or {}
            logger.info('status: {}, code: {}, shop: {}, message: {}'.format(
                response_data.get('status'),
                response_data.get('code'),
                getattr(request, 'shop', None),
                msg or response_data.get('msg')
            ))
            logger.info('SHOP HEADER KEY {}: {}'.format(x_api_key, request.META.get(x_api_key)))
            logger.info(request.META)

    def log_now(self, key=None, start=None):
        now = get_now()
        if self._DEBUG:
            start = start or self._log_now_start
            if start:
                self.logger.info('{}: took {} seconds'.format(
                    key, seconds_passed(start, now)
                ))
            else:
                # timestamp is already in logger formatter
                self.logger.info('{}'.format(key))
            self._log_now_start = now  # now is new start
        return now


class BaseFileImporter:
    _base_file_path_dir = None

    def _handle_no_file_exists(self, file_name, file_path):
        # override to handle this case
        raise Exception(f'File path does not exist: {file_path}, file_name: {file_name}')

    @property
    def base_file_path_dir(self):
        return self._base_file_path_dir or os.path.join(
            os.path.dirname(settings.BASE_DIR), 'various'
        )

    def _get_file_path(self, file_name=None):
        return os.path.join(
            self.base_file_path_dir, file_name
        )

    # some common methods, used in scrapy spiders / FashionDataFixer,
    # Note: there is actually dup in scrapy cause django/non-django code bases
    def _dump_json(self, file_name, data):
        with open(os.path.join('/tmp', file_name), 'w') as f:
            pprint(data, f)

    @cached_property
    def logger(self):
        try:
            return super().logger
        except AttributeError:
            return logger

    def _load_data_file(self, data_file_path, mode='rb', exception_result=None):
        if not os.path.exists(data_file_path):
            return exception_result
        try:
            with open(data_file_path, mode) as f:
                return pickle.load(f)
        except Exception as e:
            self.logger.info(
                e
            )
            self.logger.info(f'pickle load exception for {data_file_path}')
        return exception_result

    def _save_data_file(self, data_file_path, datas, mode='wb'):
        with open(data_file_path, mode) as f:
            pickle.dump(datas, f)

    def _save_json_file(self, file_path, data, add_base_path=False, ensure_ascii=True):
        if add_base_path:
            file_path = self._get_file_path(file_path)

        with open(file_path, 'w') as f:
            json.dump(data, f, ensure_ascii=ensure_ascii)
        return file_path

    def _load_json_file(self, file_path):
        with open(file_path, 'r') as f:
            return json.load(f)

    # end of some common methods, used in scrapy spiders / FashionDataFixer


class FileBasedCacheMixin:
    cache_version = CACHE_KEY_PREFIX  # CACHE_KEY_PREFIX includes node env db name to ensure unique-ness
    cache_timeout = CACHE_TIMEOUT

    cache = filebased_cache
    common_cache = filebased_cache

    @classmethod
    def get_cache_key(cls, key):
        # Note: both .cache_set/.cache_get methods
        # call .get_cache_key on initial key to add ENV/CACHE_VER prefix first!
        return f'{cls.cache_version}-{key}'  # we have get_cache_key function, that works slightly different

    def get_hex_cache_key(self, key):
        return get_hex_digest(self.get_cache_key(key))

    def get_qs_key(self, qs):
        try:
            return self.get_hex_cache_key(str(qs.query))
        except EmptyResultSet:
            return f'{str(id(qs))}-{random.random()}'

    def cache_get(self, key, default=None):
        key = self.get_cache_key(key)
        return self.cache.get(key, default=default)

    def cache_set(self, key, value, timeout=-1):
        if timeout == -1:
            timeout = self.cache_timeout
        key = self.get_cache_key(key)
        return self.cache.set(key, value, timeout=timeout)

    def common_cache_get(self, key, default=None):
        key = self.get_cache_key(key)
        return self.common_cache.get(key, default=default)

    def common_cache_set(self, key, value, timeout=-1):
        if timeout == -1:
            timeout = self.cache_timeout
        key = self.get_cache_key(key)
        return self.common_cache.set(key, value, timeout=timeout)

    @classproperty
    def _capture_message(cls):
        from .logging import capture_message

        return capture_message


class CommonCachedObjectMixin(FileBasedCacheMixin):
    cache_key_field = 'name'

    @cached_property
    def obj_cache_key_default(self):
        key = getattr(self, self.cache_key_field, None)  # or getattr(self, 'id', None)
        assert key, Exception(f'No cache key default: {self.__dict__}')
        return key

    @property
    def use_obj_cache(self):
        return getattr(self, '_use_obj_cache', True)

    @classproperty
    def obj_cache_class_key_prefix(cls):
        return f'obj_cache_class_{cls.__name__}'

    def obj_cache_set(self, key=None, value=None, timeout=-1):
        if not self.use_obj_cache:
            return
        key = self.get_cache_key(f'{self.obj_cache_class_key_prefix}_{key or self.obj_cache_key_default}')
        if timeout == -1:
            timeout = self.cache_timeout
        try:
            self.common_cache.set(key, value or self, timeout)
        except Exception as e:
            self._capture_message('[ObjCache] exception set', exception=e, sample=0.1, cache_key=key)

    @classmethod
    def obj_cache_get(cls, key, default=None):
        result = default
        key = cls.get_cache_key(f'{cls.obj_cache_class_key_prefix}_{key}')
        try:
            result = cls.common_cache.get(key, default)
        except Exception as e:
            cls._capture_message('[ObjCache] exception get', exception=e, sample=0.1, cache_key=key)
        return result


class JsonifyObjMixin:
    @classmethod
    def _jsonify_obj(cls, obj):
        if isinstance(obj, defaultdict):
            obj = dict(obj)
        if isinstance(obj, dict):
            return {k: cls._jsonify_obj(v) for k, v in obj.items()}
        elif isinstance(obj, (list, tuple, set)):
            return [cls._jsonify_obj(k) for k in obj]
        elif isinstance(obj, models.Model):
            return {
                'model': obj.__class__.__name__,
                'id': getattr(obj, 'pk', None) or getattr(obj, 'id', None)
            }
        elif isinstance(obj, (datetime.datetime, datetime.date)):
            return obj.strftime(DEFAULT_DATETIME_FORMAT)
        elif isinstance(obj, Decimal):
            return str(obj)

        return obj


class Parser(LoggerMixin, BaseFileImporter, FileBasedCacheMixin, JsonifyObjMixin):
    pass


class ParserException(Exception):
    pass
