image: cr.yandex/crp93o2iiq8vcbj06jit/gitlab-ci-runner:latest

before_script:
  - eval $(ssh-agent -s)
  - mkdir -m 700 -p ~/.ssh
  - echo "$SSH_PRIVATE_KEY" > ~/.ssh/id_rsa
  - chmod 600 ~/.ssh/id_rsa
  - ssh-add ~/.ssh/id_rsa
  - test $CI_COMMIT_REF_NAME != 'master' && export CI_COMMIT_REF_NAME='test'
  - export ENV="ENV_${CI_COMMIT_REF_NAME:-test}" && echo "${!ENV}" > "$(pwd)/masterchief/.env"
  - export VERSION=$(sentry-cli releases propose-version)
  - test -z $SENTRY_PROJECT && export SENTRY_PROJECT='masterchief'
  - echo "RELEASE=${SENTRY_PROJECT}@${VERSION}" >> "$(pwd)/masterchief/.env"
  - echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config

stages:
  - build
  - test
  - deploy

variables:
  DOCKER_IMAGE: $CI_REGISTRY_IMAGE:$CI_COMMIT_REF_SLUG

build:
  stage: build
  tags:
    - build
  script:
    - docker-compose rm -sf
    - docker-compose build
    - docker-compose up --no-start
    - docker commit masterchief masterchief
    - docker commit masterchief-redis masterchief-redis
    - sentry-cli releases new -p $SENTRY_PROJECT $VERSION
    - sentry-cli releases set-commits --auto $VERSION
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker tag masterchief $CI_REGISTRY/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME/masterchief:$CI_COMMIT_SHORT_SHA
    - docker push $CI_REGISTRY/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME/masterchief:$CI_COMMIT_SHORT_SHA
    - docker tag masterchief-redis $CI_REGISTRY/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME/masterchief-redis:$CI_COMMIT_SHORT_SHA
    - docker push $CI_REGISTRY/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME/masterchief-redis:$CI_COMMIT_SHORT_SHA
#  script:
#    - docker build -t $DOCKER_IMAGE .
#    - docker push $DOCKER_IMAGE
  only:
    - master
    - test

#test:
#  stage: test
#  image: python:3.11
#  script:
#    - pip install -r requirements.txt
#    - python manage.py test
#  only:
#    - master
#    - test
test:
  stage: test
  tags:
    - build
  script:
    - docker-compose up -d
    - docker exec -t masterchief python manage.py test --nomigrations --settings=masterchief.settings.test_settings
  only:
    - test
    - master

#deploy:
#  stage: deploy
#  image: docker:20.10.16
#  services:
#    - docker:20.10.16-dind
#  script:
#    - docker-compose down
#    - docker-compose pull
#    - docker-compose up -d
#  only:
#    - master