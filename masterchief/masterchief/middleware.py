from django.http import StreamingHttpResponse
from masterchief.utils.async_helpers import sync_to_async_iterator
import asyncio


class AsyncStreamingMiddleware:
    """
    Middleware to automatically convert synchronous iterators to asynchronous ones
    for StreamingHttpResponse objects.
    """
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        response = self.get_response(request)

        # Check if the response is a StreamingHttpResponse
        if isinstance(response, StreamingHttpResponse) and hasattr(response, 'streaming'):
            # Convert the synchronous iterator to an asynchronous one
            response.streaming_content = sync_to_async_iterator(response.streaming_content)

        return response

    async def __call_async__(self, request):
        response = await self.get_response(request)

        # Check if the response is a StreamingHttpResponse
        if isinstance(response, StreamingHttpResponse) and hasattr(response, 'streaming'):
            # Convert the synchronous iterator to an asynchronous one
            response.streaming_content = sync_to_async_iterator(response.streaming_content)

        return response
