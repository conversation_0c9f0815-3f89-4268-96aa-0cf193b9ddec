from __future__ import absolute_import, unicode_literals
import os
import sys

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'masterchief.settings')

from celery import Celery
from masterchief.settings.celery_config import CELERY_CONFIG

# Create the Celery app
app = Celery('masterchief')

# Configure Celery using the dictionary from masterchief.settings.celery_config
app.conf.update(CELERY_CONFIG)

# Auto-discover tasks in all installed apps
app.autodiscover_tasks()


@app.task(bind=True)
def debug_task(self):
    """Debug task to verify <PERSON><PERSON><PERSON> is working correctly."""
    print(f'Request: {self.request!r}')
