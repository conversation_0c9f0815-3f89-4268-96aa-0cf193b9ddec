import os
import logging.config

from .paths import BASE_DIR
from .env_vars import LOG_LEVEL

LOGGING_CONFIG = None


logging.config.dictConfig({
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'console': {
            # exact format is not important, this is the minimum information
            'format': '%(asctime)s %(name)-12s %(levelname)-8s %(message)s',
        },
        'verbose': {
            'format': '%(levelname)s %(asctime)s %(name)s.%(funcName)s:%(lineno)s %(message)s'
        },
        'simple': {
            'format': '%(asctime)s %(levelname)s %(name)s:%(lineno)s %(message)s'
        },
        'custom': {
            'format': '%(message)s'
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'console',
        },
        'console_handler': {
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
        'file_handler': {
            'formatter': 'simple',
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, 'logs', 'masterchief.log'),  # TODO: special logs directory?
            'maxBytes': 10 * 1024 * 1024,
            'backupCount': 5,
            'delay': True
        },
        'custom_file_handler': {
            'formatter': 'custom',
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': os.path.join(BASE_DIR, 'logs', 'custom.log'),  # TODO: special logs directory?
            'maxBytes': 10 * 1024 * 1024,
            'backupCount': 5,
            'delay': True
        }
        # TODO: Add Handler for Sentry for `warning` and above
        # 'sentry': {
        #     'level': 'WARNING',
        #     'class': 'raven.contrib.django.raven_compat.handlers.SentryHandler',
        # },
    },
    'loggers': {
        # 'django.db.backends': {
        #     'handlers': ['console'],
        #     'level': 'DEBUG',
        # },
        # root logger
        '': {
            'level': 'WARNING',
            'handlers': ['console', ]  # 'sentry'],
        },

        'mail_admins': {
            'level': 'CRITICAL',
            # 'filters': ['require_debug_false'],
            'class': 'django.utils.log.AdminEmailHandler'
        },

        'filters': {
                'require_debug_false': {
                    '()': 'django.utils.log.RequireDebugFalse'
                },
                'require_debug_true': {
                    '()': 'django.utils.log.RequireDebugTrue'
                }
        },
        'masterchief': {
            'level': LOG_LEVEL,
            'handlers': ['console_handler', 'file_handler'],
            'formatter': '',
            # required to avoid double logging with root logger
            'propagate': False,
        },
        'custom': {
            'level': LOG_LEVEL,
            'handlers': ['custom_file_handler'],
            'formatter': '',
            # required to avoid double logging with root logger
            'propagate': False,
        }
    },
})

