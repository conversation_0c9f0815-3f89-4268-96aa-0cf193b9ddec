from . import *  # noqa


DEBUG = True

# DATABASE TO MAKE TESTS IN MEMORY DB
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',
    }
}

IN_TESTS=True
NODE_ENV = 'test'
NOT_USE_KIBANA = True
if NOT_USE_KIBANA and 'elasticapm.contrib.django' in INSTALLED_APPS:
    INSTALLED_APPS.remove('elasticapm.contrib.django')


DEBUG_TOOLBAR_CONFIG = {'IS_RUNNING_TESTS': False}
if 'debug_toolbar' in INSTALLED_APPS:
    INSTALLED_APPS.remove('debug_toolbar')

# django 4
CSRF_TRUSTED_ORIGINS = "https://*.domain.ai https://*.domain.ru".split()
