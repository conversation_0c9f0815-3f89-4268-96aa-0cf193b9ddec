from celery.schedules import crontab
from kombu import Exchange, Queue

from .env_vars import REDIS_URI

# SSL settings for secure Redis connections
BROKER_USE_SSL = REDIS_BACKEND_USE_SSL = None
if 'rediss://' in REDIS_URI and 'ssl_cert_reqs' not in REDIS_URI:
    # For secure Redis connections, configure SSL settings
    import ssl
    BROKER_USE_SSL = REDIS_BACKEND_USE_SSL = {
        'ssl_cert_reqs': ssl.CERT_NONE
    }

# if 'rediss://' in REDIS_URI and 'ssl_cert_reqs' not in REDIS_URI:
#     # by default we are --insecure
#     # https://github.com/celery/celery/issues/5371
#     # from urllib.parse import urlencode
#     # query_string_params = urlencode({'ssl_cert_reqs': 'CERT_NONE'})
#     # REDIS_URI += f'?{query_string_params}'
#
#     # https://stackoverflow.com/a/********
#     import ssl
#     broker_use_ssl = redis_backend_use_ssl = {
#         'ssl_cert_reqs': ssl.CERT_NONE
#     }

# Task exchange for routing
TASK_EXCHANGE = Exchange('tasks', type='direct')

# Celery configuration dictionary
CELERY_CONFIG = {
    # Broker and result backend settings
    'broker_url': REDIS_URI,
    'result_backend': REDIS_URI,
    'task_ignore_result': True,

    # Serialization settings
    'accept_content': ['application/json'],
    'result_serializer': 'json',
    'task_serializer': 'json',
    'timezone': 'UTC',

    # Retry settings
    'broker_transport_options': {
        'max_retries': 5,
        'interval_start': 0,
        'interval_step': 0.5,
        'interval_max': 3,
    },

    # Beat scheduler settings
    'beat_scheduler': 'django_celery_beat.schedulers:DatabaseScheduler',
    'beat_max_loop_interval': 600,

    # Connection retry settings
    'broker_connection_retry': True,
    'broker_connection_retry_on_startup': True,
    'broker_channel_error_retry': True,

    # Task routing
    'task_queues': [
        Queue('celery', TASK_EXCHANGE, routing_key='celery'),
        Queue('highload', TASK_EXCHANGE, routing_key='highload'),
    ],

    'task_routes': {
        # yet no highload queue
        # 'data.tasks.highload_*': 'highload',
        '*': 'celery',
    },

    # beat schedule
    'beat_schedule': {
        'task_to_test': {
            'task': 'masterchief.tasks.task_stub',
            'schedule': crontab(minute='*/2', hour='*'),  # just test task every 2 minutes
        },
        'sync_vendors': {
            'task': 'data.tasks.task_sync_vendors',
            'schedule': crontab(minute='31', hour='*/3'),  # sync vendors every 3 hours
        },
        'sync_enabled_vendors_products': {
            'task': 'data.tasks.task_sync_enabled_vendors_products',
            'schedule': crontab(minute='13,43', hour='*'),  # sync enabled vendors products every 30 minutes
        },
        'classify_products': {
            'task': 'services.classifier.tasks.task_classify_batch_products',
            'schedule': crontab(minute='*/5', hour='*'),  # classify products every 5 minutes
            'kwargs': {'batch_size': 5}  # process 5 products at a time
        },
    }
}

# Add SSL configuration if needed
if BROKER_USE_SSL:
    CELERY_CONFIG['broker_use_ssl'] = BROKER_USE_SSL
    CELERY_CONFIG['redis_backend_use_ssl'] = REDIS_BACKEND_USE_SSL
