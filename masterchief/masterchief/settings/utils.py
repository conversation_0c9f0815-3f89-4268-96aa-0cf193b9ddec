import os


def get_env(key, default_value=None, value_type=None):
    value = os.getenv(key, default_value)
    if value_type:
        try:
            value = value_type(value)
        except:
            value = default_value
    return value


def get_env_bool(key, default_value=False):
    value = get_env(key, default_value=None)
    if value is None:
        return default_value
    return value in ('1', 'True')


def get_env_int(key, default_value=False):
    return get_env(key, default_value=default_value, value_type=int)


def get_env_list(key, sep=' ', default_value=None, value_type=None):
    value = get_env(key, default_value=default_value)
    splits = [y for y in (x.strip() for x in value.split(sep)) if y]
    if value_type:
        splits = [value_type(x) for x in splits]
    return splits


# to support internationalization
def _(x):
    return x
