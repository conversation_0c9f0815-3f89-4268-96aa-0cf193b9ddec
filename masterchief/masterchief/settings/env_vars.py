# this module should contain all the variables from environment
# we should try to do get_env (aka os.getenv) or get_env_bool *only* in this file (project-wide)

from .utils import get_env, get_env_bool, get_env_int, get_env_list

DB_HOST = get_env('DB_HOST', 'db_host')   # db host
DB_PORT = get_env('DB_PORT', 'db_port')
DB_NAME = get_env('DB_NAME', 'db_name')   # name of database

DB_USER = get_env('DB_USER', 'postgres')
DB_PASSWORD = get_env('DB_PASSWORD', 'postgres')

# https://docs.djangoproject.com/en/5.2/ref/databases/#persistent-connections
CONN_MAX_AGE = get_env_int('CONN_MAX_AGE', 180)

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = get_env_bool('DEBUG', False)
LOG_LEVEL = get_env('LOG_LEVEL', 'info').upper()

NODE_ENV = get_env('NODE_ENV')
if not NODE_ENV:
    DJANGO_SETTINGS_MODULE = get_env('DJANGO_SETTINGS_MODULE', '')
    if DJANGO_SETTINGS_MODULE.endswith('.test_settings'):
        NODE_ENV = 'test'
    else:
        NODE_ENV = 'staging'
        if not DEBUG and DB_NAME == 'masterchief':  #  and DB_USER == 'masterchief':
            # 2023.09.19, "production_ru" / "production_ai" possible values
            NODE_ENV = 'production'

NODE_ON_PRODUCTION = NODE_ENV.startswith('production')
NODE_NAME = get_env('NODE_NAME', None)

# sentry release integration
RELEASE = get_env('RELEASE', 'masterchief@')

# USE_KIBANA_APM is enabled by default, set USE_KIBANA_APM=0 in CI envs to disable kibana apm usage
USE_KIBANA_APM = get_env_bool('USE_KIBANA_APM', default_value=True)
KIBANA_SERVER_URL = get_env('KIBANA_SERVER_URL', default_value='https://apm.garderobo.ai')
SENTRY_DSN = get_env('SENTRY_DSN', default_value='https://<EMAIL>/6')

# Note: 2022.04.12,  domain/area related changes
DOMAIN = get_env('DOMAIN', default_value='garderobo.ai')
# example: 'RU' or 'AI'
DOMAIN_AREA = get_env('DOMAIN_AREA', default_value=None)
if not DOMAIN_AREA and DOMAIN:
    # garderobo.ru -> "ru", garderobo.ai -> "ai"
    DOMAIN_AREA = DOMAIN.rsplit('.', 1)[-1]
DOMAIN_AREA = (DOMAIN_AREA or 'ai').upper()
CURRENT_HOST_NAME = get_env('CURRENT_HOST_NAME', default_value='masterchief')

# in case of None will be combined from "CURRENT_HOST_NAME.DOMAIN"
CURRENT_HOST = get_env('CURRENT_HOST', default_value=None)
CLIENT_SERVER_HOST = get_env('CLIENT_SERVER_HOST', default_value=None)

USE_GCS_PD = get_env_bool('USE_GCS_PD', False)
GCS_CREDENTIALS_FILE_PATH = get_env('GCS_CREDENTIALS_FILE_PATH', '/opt/client/gc_keys.json')

# https://docs.djangoproject.com/en/5.2/ref/settings/#allowed-hosts
ALLOWED_HOSTS = get_env_list('ALLOWED_HOSTS', default_value=f'.{DOMAIN}')
# https://docs.djangoproject.com/en/5.2/ref/settings/#csrf-trusted-origins
CSRF_TRUSTED_ORIGINS = get_env_list('CSRF_TRUSTED_ORIGINS', default_value=f'https://*.{DOMAIN}')

CACHE_VERSION = get_env('CACHE_VERSION', None)  # cache version default in retail.config

CLIENT_SERVICE_API_TOKEN = get_env('CLIENT_SERVICE_API_TOKEN', 'KyavtdLqeNNU0bPzQcCgYpLudPJM25Cg')
X_API_TOKEN = get_env('X_API_TOKEN', None)
X_API_TOKEN = X_API_TOKEN or CLIENT_SERVICE_API_TOKEN

# Redis connection settings
REDIS_URI = get_env('REDIS_URI', default_value='redis://localhost:6379/13')
