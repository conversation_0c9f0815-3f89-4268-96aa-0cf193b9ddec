from .env_vars import (
    DB_HOST, DB_NAME, DB_PORT, DB_USER, DB_PASSWORD, CONN_MAX_AGE
)


# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases

# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.sqlite3',
#         'NAME': BASE_DIR / 'db.sqlite3',
#     }
# }

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'HOST': DB_HOST,
        'PORT': DB_PORT,
        'NAME': DB_NAME,
        'USER': DB_USER,
        'PASSWORD': DB_PASSWORD,

        # or 300 and no "--pool eventlet --concurrency 100" for celery,
        # but just prefork 2 workers for 2 CPU

        'CONN_MAX_AGE': CONN_MAX_AGE,
        # 'CONN_MAX_AGE': 0,

        # 'OPTIONS': {
        #     'sslmode': 'require',
        # },
        # to run celerybeat as celery, we can use "prefer" as "sslmode" here
        # cause celery tasks are run as celery uid also, we use prefer (OR remove --uid celery!)
        'OPTIONS': {
            # Remove SSL requirements or specify accessible cert path
            'sslmode': 'prefer',  # Change from 'require' to 'prefer' or 'disable'
        },
    }
}

# as an option, we can do it as
# import dj_database_url
# DATABASES['default'] = dj_database_url.parse(
#     'postgres://<DB_USER>:<DB_PASSWORD>@<DB_HOST>:<DB_POST>/<DB_NAME>',
#     conn_max_age=600,
#     # engine=postgres_engine
# )
