import asyncio
from functools import wraps
from asgiref.sync import sync_to_async

def sync_to_async_iterator(sync_iterator):
    """Convert a synchronous iterator to an asynchronous one."""
    async def async_iterator():
        for item in sync_iterator:
            yield item
            # Give other tasks a chance to run
            await asyncio.sleep(0)
    return async_iterator()

def async_streaming_response(view_func):
    """Decorator to convert a synchronous streaming response to an asynchronous one."""
    @wraps(view_func)
    async def wrapper(request, *args, **kwargs):
        response = await sync_to_async(view_func)(request, *args, **kwargs)
        if hasattr(response, 'streaming') and response.streaming:
            response.streaming_content = sync_to_async_iterator(response.streaming_content)
        return response
    return wrapper