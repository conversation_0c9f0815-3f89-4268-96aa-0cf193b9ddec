"""
URL configuration for masterchief project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static

from api.service.router import service_api_router
from data.views import index_view

urlpatterns = [
    path('admin/', admin.site.urls),

    # all another service api methods, i.e. /v3/products/update
    path('api/v3/',
         include((service_api_router.urls, 'api-service-v3'), namespace='api-service-v3')),

    path('', index_view, name='index'),
]

# Serve static files in development
# if settings.DEBUG:
# Let we serve staticfiles from django as well even on production
urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
