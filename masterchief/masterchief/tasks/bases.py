# coding: utf-8
from warnings import warn
from celery import shared_task, Task as BaseTask


from django.core.management import call_command
from django.utils import timezone
from django.core.management import call_command as call_django_command, CommandError
from django.db.utils import OperationalError, InternalError

from common import capture_message, logger
# from masterchief.config import HIGHLOAD_QUEUE_NAME, INTEGRATIONS_QUEUE_NAME

from .locks import single_running_task, skip_if_running  # noqa


RETRY_ON_FAILURE_EXCEPTIONS = (OperationalError, InternalError)


from masterchief.celery_app import app


class CeleryTasks:
    """Helper class for Celery tasks"""

    @staticmethod
    def get_task_by_name(task_name):
        """Get a task by its name"""
        return app.tasks.get(task_name)


@shared_task
def call_command_task(name, *args, **options):
    """Run a Django management command as a Celery task"""
    call_command(name, *args, **options)


@shared_task
def example_task():
    """Example task to demonstrate Celery functionality"""
    now = timezone.now()
    return f"Task executed at {now}"


@shared_task
def periodic_cleanup():
    """Example periodic task for database cleanup"""
    # This is just a placeholder for a real cleanup task
    now = timezone.now()
    return f"Cleanup executed at {now}"


def run_task(*task_args, **task_kwargs):
    # run_task_async=True, run_task_async_method=None, run_task_command_method='call_command_and_retry'
    run_task_async = task_kwargs.pop('run_task_async', True)
    run_task_async_method = task_kwargs.pop('run_task_async_method', run_task_in_highload_queue)
    run_task_command_method = task_kwargs.pop('run_task_command_method', call_command_and_retry)

    # run_task_check_single_run = \
    #     run_task_async and task_kwargs.pop('run_task_check_single_run', False)
    # if run_task_check_single_run and CeleryTasks.same_task_is_already_in_queues(*task_args, **task_kwargs):
    #     return False

    if run_task_async:
        run_task_async_method(
            run_task_command_method,  # call_command_and_retry/call_command?
            *task_args,
            **task_kwargs
        )
    else:
        # sync way, just call django command sync way
        run_task_command_method(
            *task_args, **task_kwargs
        )


def run_task_in_highload_queue(task_command, *args, countdown=None, **kwargs):
    return task_command.apply_async(
        args=args,
        kwargs=kwargs,
        # queue=HIGHLOAD_QUEUE_NAME,
        countdown=countdown
    )
    # sync way for debug only!
    # return task_command.run(*args, **kwargs)


def run_task_in_integrations_queue(task_command, *args, countdown=None, **kwargs):
    return task_command.apply_async(
        args=args,
        kwargs=kwargs,
        # queue=INTEGRATIONS_QUEUE_NAME,
        countdown=countdown
    )


@shared_task
def call_command(name, *args, **options):
    call_django_command(name, *args, **options)


@shared_task
def capture_message_task(*args, **kwargs):
    capture_message(*args, **kwargs)


class Task(BaseTask):
    # TODO: tasks separate queue with rate limitations
    max_retries = 5
    ignore_result = True
    # rate_limit = '50/m'
    # default_retry_delay = 3 * 60

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        try:
            if type(exc) in RETRY_ON_FAILURE_EXCEPTIONS:
                return self.retry(args=args, kwargs=kwargs, countdown=3 * self.request.retries)
        except Exception as e:
            message = f'while retry on {type(exc)} {exc} we got another exception: {e}'
            logger.warning(message)
            warn(message)

        msg = 'Task failed: {}, exception: {} {}'.format(getattr(self, 'name', '-'), type(exc), exc)
        capture_message(msg, exception=exc, task_id=task_id, task_args=args, task_kwargs=kwargs)
        msg = 'Task failed: {} task_id: {}, args: {}, kwargs: {}, einfo: {}'.format(
            getattr(self, 'name', '-'), task_id, args, kwargs, einfo)
        warn(msg)
        return None


@shared_task(bind=True, base=Task)
def call_command_and_retry(self, *args, **kwargs):
    try:
        call_django_command(*args, **kwargs)
    except (CommandError, AssertionError) as exc:
        logger.warning(f'Task: {args}, {str(kwargs)[:100]} got CommandError exception: "{exc}"')
    except Exception as exc:
        logger.warning(f'Task: {args}, {str(kwargs)[:100]} got an Exception: "{exc}", and will be retried')
        logger.exception(exc)
        raise self.retry(exc=exc)
