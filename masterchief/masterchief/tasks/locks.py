from functools import wraps

from redis import Redis
import redis_lock

from django.conf import settings
from common import logger

REDIS_URI = settings.REDIS_URI
REDIS_CONN = None
LOCKS_PREFIX = f'client-server-locks-v20210224-{settings.NODE_ENV}'


def skip_if_running(f):
    """
    Based on https://stackoverflow.com/a/35197449
    prevent multiple but same tasks to be run simultaneously
    (cause multiple celery beat instances can schedule tasks at once, TODO: beat on single node?)
    """
    task_name = f'{f.__module__}.{f.__name__}'

    @wraps(f)
    def wrapped(self, *args, **kwargs):
        workers = self.app.control.inspect().active()

        for worker, tasks in workers.items():
            for task in tasks:
                # also can check for args / kwargs
                # but atm we should not  # tuple(args) == tuple(task['args']) and kwargs == task['kwargs'] and...
                if task_name == task['name'] and self.request.id != task['id']:
                    logger.error(f'task {task_name} ({args}, {kwargs}) is running on {worker}, skipping')

                    return None
        logger.error(f'Single running task is now actually running: {task_name}')
        return f(self, *args, **kwargs)

    return wrapped


def get_redis_connection():
    global REDIS_CONN

    if not REDIS_CONN:
        REDIS_CONN = Redis.from_url(REDIS_URI)  # redis://...:port/db_num
    return REDIS_CONN


def get_named_lock(name, conn=None, prefix=LOCKS_PREFIX, **lock_kwargs):
    conn = conn or get_redis_connection()
    name = f'{prefix}-{name}'
    named_lock = redis_lock.Lock(
        conn,
        name,
        **lock_kwargs
    )
    named_lock.full_name = name
    return named_lock


def lock_and_work(lock_name, method, blocking=False, *method_args, **method_kwargs):
    error = None
    result = work_done = lock_acquired = False

    lock = get_named_lock(lock_name)
    if lock.acquire(blocking=blocking):
        lock_acquired = True
        try:
            result = method(*method_args, **method_kwargs)
            work_done = True
        except Exception as e:
            error = e
        lock.release()
    else:
        pass
    return {
        'result': result,
        'error': error,
        'lock_acquired': lock_acquired,
        'work_done': work_done,
    }


def single_running_task(lock_name=None, on_fail=False,
                        timeout=None, blocking=False, **lock_kwargs):
    """
    used to prevent duplicates tasks execution in case when multiple celery beats on nodes
    schedule tasks without check for task is already scheduled
    if not specified, lock name will be function.__name__
    """
    def inner_function(function):
        @wraps(function)
        def wrapper(*args, **kwargs):
            lock = get_named_lock(
                lock_name or function.__name__, **lock_kwargs)
            full_name = lock.full_name
            if lock.acquire(blocking=blocking, timeout=timeout):
                logger.error(f'Lock acquired: {full_name}, function {function.__name__}')
                try:
                    result = function(*args, **kwargs)
                except Exception as e:
                    logger.exception(e)
                    logger.error(f'got exception while single_running_task for function: {function}')
                lock.release()
                return result
            else:
                logger.error(f'Cannot acquire lock: {full_name}, function {function.__name__}')
                if on_fail:
                    return on_fail(*args, **kwargs)
        return wrapper

    return inner_function
