from django.conf import settings

# DEFAULT DATETIME FORMAT
DEFAULT_DATETIME_FORMAT = '%Y-%m-%d %H:%M:%S'
DAY_CACHE_TIMEOUT = 24 *3600

NODE_ENV_DB_NAME = settings.DATABASES['default']['NAME']
NODE_ENV_DB_HOST = \
    f"{settings.DATABASES['default'].get('HOST') or '-'}:{settings.DATABASES['default'].get('PORT') or '-'}"
NODE_ENV = getattr(settings, 'NODE_ENV', 'test')
NODE_NAME = getattr(settings, 'NODE_NAME', None)
RELEASE = getattr(settings, 'RELEASE', 'masterchief@')

SENTRY_DSN = getattr(settings, 'SENTRY_DSN', 'https://<EMAIL>/6')

# Note: 2022.04.12,  domain/area related changes
DOMAIN = getattr(settings, 'DOMAIN', 'garderobo.ru')
NODE_ON_PRODUCTION = getattr(settings, 'NODE_ON_PRODUCTION', False)

CURRENT_HOST_NAME = getattr(settings, 'CURRENT_HOST_NAME', None)
if not CURRENT_HOST_NAME:
    CURRENT_HOST_NAME = 'masterchief'

CURRENT_HOST = getattr(settings, 'CURRENT_HOST', None)
if not CURRENT_HOST:
    # i.e https://api.garderobo.ai
    CURRENT_HOST = f'https://{CURRENT_HOST_NAME}.{DOMAIN}'

CLIENT_HOST_ADMIN_URL = f'{CURRENT_HOST}/admin'

CLIENT_SERVER_HOST = getattr(settings, 'CLIENT_SERVER_HOST', None)
if not CLIENT_SERVER_HOST:
    # BACKEND HOST, # Note: 2022.04.12,  domain/area related changes
    CLIENT_SERVER_HOST_NAME = 'testapi'
    if NODE_ON_PRODUCTION:
        BACKEND_HOST_NAME = 'api'
    CLIENT_SERVER_HOST = f'https://{CLIENT_SERVER_HOST_NAME}.{DOMAIN}'

CLIENT_SERVER_HOST_ADMIN_URL = f'{CLIENT_SERVER_HOST}/admin'
CLIENT_SERVER_HOST_DEMO_URL = f'{CLIENT_SERVER_HOST}/demo'


# CACHE RELATED VARS
CACHE_VERSION = getattr(settings, 'CACHE_VERSION', None) or 'v2025-05-05'  # cache version updated
# Note: use CACHE_KEY_PREFIX instead of CACHE_VERSION
CACHE_TIMEOUT = 7200

# this key should be updated on local dev when you switch from test to prod db env,
# cause envs db ids are different
CACHE_KEY_PREFIX = f'{CACHE_VERSION}-{NODE_ENV_DB_NAME}-{NODE_ENV_DB_HOST}-'


CLIENT_SERVICE_API_TOKEN = settings.CLIENT_SERVICE_API_TOKEN
X_API_TOKEN = settings.X_API_TOKEN or CLIENT_SERVICE_API_TOKEN
