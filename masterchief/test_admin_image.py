#!/usr/bin/env python
"""
Простой тест для проверки работы отображения изображений в админке
"""
import os
import sys
import django

# Добавляем путь к проекту
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Настраиваем Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'masterchief.settings')
django.setup()

from data.models import Product, Vendor

def test_image_extraction():
    """Тестируем извлечение изображений из данных товара"""
    
    # Создаем тестовый вендор
    vendor, created = Vendor.objects.get_or_create(
        name="Test Vendor",
        defaults={'code': 'test'}
    )
    
    # Тестовые данные товара с изображениями (как в примере)
    test_data = {
        "id": 1798110,
        "rc": "Платья, сарафаны > Мини",
        "name": "Платье мини на тонких лямках \"WILD EGO\"",
        "group": "Платья",
        "rc_id": 203,
        "gender": "Жен.",
        "season": None,
        "brand_id": 15659,
        "group_id": 66,
        "material": "",
        "pictures": [
            {
                "id": 5044785,
                "url": "https://media.garderobo.ru/yuliawave/big/25657299fb8078de0a565c288ad2228d.jpg",
                "is_fdh": True,
                "is_primary": True
            },
            {
                "id": 5045955,
                "url": "https://media.garderobo.ru/yuliawave/big/78239e011dcc18c1f49a4150e3d75a7d.jpg"
            },
            {
                "id": 5045956,
                "url": "https://media.garderobo.ru/yuliawave/big/698422f73c6729b89958f68a8f0d9dc3.jpg"
            }
        ],
        "gender_id": 1,
        "season_id": None,
        "vendor_id": 199,
        "brand_name": "YULIAWAVE",
        "group_code": "layer-full_dress",
        "is_deleted": False,
        "picture_id": 5044785,
        "description": "",
        "gender_code": 2,
        "picture_url": "https://media.garderobo.ru/yuliawave/big/25657299fb8078de0a565c288ad2228d.jpg",
        "vendor_color": "Черный",
        "vendor_category_id": "1105",
        "vendor_category_name": "Платья"
    }
    
    # Создаем или обновляем тестовый товар
    product, created = Product.objects.update_or_create(
        id=1798110,
        defaults={
            'name': test_data['name'],
            'vendor': vendor,
            'data': test_data,
            'is_deleted': False
        }
    )
    
    print(f"Товар {'создан' if created else 'обновлен'}: {product.name}")
    
    # Тестируем извлечение изображения
    image_url = product.get_primary_image_url()
    print(f"URL изображения: {image_url}")
    
    # Проверяем, что извлекается правильное изображение
    expected_url = "https://media.garderobo.ru/yuliawave/big/25657299fb8078de0a565c288ad2228d.jpg"
    
    if image_url == expected_url:
        print("✅ Тест прошел успешно! Извлекается правильное изображение.")
    else:
        print(f"❌ Тест не прошел. Ожидался: {expected_url}, получен: {image_url}")
    
    # Тестируем случай с picture_url
    test_data_2 = {
        "picture_url": "https://example.com/test-image.jpg",
        "name": "Test Product 2"
    }
    
    product_2, created = Product.objects.update_or_create(
        id=1798111,
        defaults={
            'name': test_data_2['name'],
            'vendor': vendor,
            'data': test_data_2,
            'is_deleted': False
        }
    )
    
    image_url_2 = product_2.get_primary_image_url()
    print(f"URL изображения для товара 2: {image_url_2}")
    
    if image_url_2 == "https://example.com/test-image.jpg":
        print("✅ Тест 2 прошел успешно!")
    else:
        print(f"❌ Тест 2 не прошел. Получен: {image_url_2}")
    
    # Тестируем случай без изображений
    test_data_3 = {
        "name": "Test Product 3 - No Images"
    }
    
    product_3, created = Product.objects.update_or_create(
        id=1798112,
        defaults={
            'name': test_data_3['name'],
            'vendor': vendor,
            'data': test_data_3,
            'is_deleted': False
        }
    )
    
    image_url_3 = product_3.get_primary_image_url()
    print(f"URL изображения для товара 3 (без изображений): {image_url_3}")
    
    if image_url_3 is None:
        print("✅ Тест 3 прошел успешно! Корректно обрабатывается отсутствие изображений.")
    else:
        print(f"❌ Тест 3 не прошел. Ожидался None, получен: {image_url_3}")

if __name__ == "__main__":
    test_image_extraction()
