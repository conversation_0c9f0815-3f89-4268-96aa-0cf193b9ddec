version: '3.8'

services:
  masterchief:
    container_name: 'masterchief'
    build: .
    command: gunicorn masterchief.asgi:application --bind 0.0.0.0:8000 --workers 4 --worker-class uvicorn.workers.UvicornWorker --log-level debug
    volumes:
      - .:/app
      # - ./static:/app/static
      # - ./media:/app/media
    ports:
      - "8000:8000"
    env_file:
      - .env.docker
    network_mode: "host"
    restart: unless-stopped
    depends_on:
      - redis

  redis:
    container_name: 'masterchief-redis'
    image: redis:alpine
    command: redis-server /usr/local/etc/redis/redis.conf
    ports:
      - "6380:6380"
    volumes:
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    #    volumes:
    #      - redis_data:/data
    restart: unless-stopped
    network_mode: "host"

#volumes:
#  static_volume:
#  media_volume:
#  redis_data: