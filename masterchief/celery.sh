#!/bin/bash
[ -f .env ] && source .env

logfile="logs/celery.log"
concurrency="${CELERY_CONCURRENCY:-2}"
# concurrency_highload="${CELERY_CONCURRENCY_HIGHLOAD:-1}"

multiplier=1  # defaults to 4 per worker thread

max_tasks_per_child=7200            # defaults to unlim
max_memory_per_child=450000         # kb, ~430MB
high_max_tasks_per_child=1024       # highload queue, less tasks before worker reload
high_max_memory_per_child=900000    # kb, ~850MB, highload queue, more memory can be consumed, but need limit also

case "$1" in
    start)
        [ ! -z $NO_CELERY ] && exit 0

        # Create logs directory if it doesn't exist
        mkdir -p logs
        chmod o+wx logs

        # Standard worker for default queue
        nohup celery -A masterchief worker -l INFO --concurrency $concurrency \
          --logfile $logfile --hostname worker@%h --prefetch-multiplier=$multiplier \
          --max-tasks-per-child=$max_tasks_per_child --max-memory-per-child=$max_memory_per_child \
          --uid=celery --gid=celery &

        # Worker for highload queue
        # nohup celery -A masterchief worker -l INFO --concurrency $concurrency_highload --pool prefork --prefetch-multiplier=1 -Ofair -Q highload --hostname highloadworker@%h --max-tasks-per-child=$high_max_tasks_per_child --max-memory-per-child=$high_max_memory_per_child &

        # Start the beat scheduler, warning: we run it as root, cause postgres on host requires root access atm / ssl
        # --uid=celery --gid=celery
        nohup celery -A masterchief beat -l INFO --scheduler django_celery_beat.schedulers:DatabaseScheduler \
          --logfile logs/celerybeat.log &
        ;;
    stop)
        pkill -f "celery -A masterchief" || echo "No celery processes found"
        ;;
    restart)
        $0 stop
        sleep 2
        $0 start
        ;;
    status)
        ps aux | grep "celery -A masterchief" | grep -v grep || echo "No celery processes running"
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|status}"
        exit 1
        ;;
esac

exit 0
