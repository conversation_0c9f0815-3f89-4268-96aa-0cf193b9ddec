import json
import os
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from django.db import transaction

from data.models import Category, Subcategory


class Command(BaseCommand):
    help = 'Load categories and subcategories from categories.json file'

    def add_arguments(self, parser):
        parser.add_argument(
            '--file',
            type=str,
            default='categories.json',
            help='Path to categories JSON file (default: categories.json in masterchief folder)',
        )
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing categories and subcategories before loading',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be loaded without actually doing it',
        )

    def handle(self, *args, **options):
        # Определяем путь к файлу
        if os.path.isabs(options['file']):
            categories_file_path = options['file']
        else:
            categories_file_path = os.path.join(settings.BASE_DIR, 'masterchief', options['file'])
        
        if not os.path.exists(categories_file_path):
            raise CommandError(f'Categories file not found: {categories_file_path}')

        # Загружаем данные из файла
        try:
            with open(categories_file_path, 'r', encoding='utf-8') as f:
                categories_data = json.load(f)
        except Exception as e:
            raise CommandError(f'Error reading categories file: {e}')

        if options['dry_run']:
            self.stdout.write(self.style.WARNING('DRY RUN - no changes will be made'))
            self.show_data_preview(categories_data)
            return

        if options['clear']:
            self.clear_existing_data()

        self.load_categories_data(categories_data)

    def show_data_preview(self, categories_data):
        """Показывает превью данных, которые будут загружены"""
        total_categories = len(categories_data)
        total_subcategories = sum(len(list(cat.values())[0]) for cat in categories_data)
        
        self.stdout.write(f'Found {total_categories} categories and {total_subcategories} subcategories')
        self.stdout.write('')
        
        for i, category_item in enumerate(categories_data[:3]):  # Показываем первые 3 категории
            for category_name, subcategories_list in category_item.items():
                self.stdout.write(f'Category: {category_name} ({len(subcategories_list)} subcategories)')
                for j, subcategory in enumerate(subcategories_list[:3]):  # Показываем первые 3 подкатегории
                    self.stdout.write(f'  - {subcategory["name"]}')
                    if subcategory.get('description'):
                        desc_preview = subcategory['description'][:100] + '...' if len(subcategory['description']) > 100 else subcategory['description']
                        self.stdout.write(f'    Description: {desc_preview}')
                if len(subcategories_list) > 3:
                    self.stdout.write(f'    ... and {len(subcategories_list) - 3} more subcategories')
                self.stdout.write('')
        
        if total_categories > 3:
            self.stdout.write(f'... and {total_categories - 3} more categories')

    def clear_existing_data(self):
        """Удаляет существующие категории и подкатегории"""
        with transaction.atomic():
            subcategories_count = Subcategory.objects.count()
            categories_count = Category.objects.count()
            
            Subcategory.objects.all().delete()
            Category.objects.all().delete()
            
            self.stdout.write(
                self.style.WARNING(
                    f'Cleared {subcategories_count} subcategories and {categories_count} categories'
                )
            )

    def load_categories_data(self, categories_data):
        """Загружает данные категорий и подкатегорий"""
        created_categories = 0
        created_subcategories = 0
        updated_subcategories = 0
        
        with transaction.atomic():
            for category_item in categories_data:
                for category_name, subcategories_list in category_item.items():
                    # Создаем или получаем категорию
                    category, created = Category.objects.get_or_create(
                        name=category_name,
                        defaults={'is_deleted': False}
                    )
                    
                    if created:
                        created_categories += 1
                        self.stdout.write(f'Created category: {category_name}')
                    
                    # Создаем подкатегории
                    for subcategory_data in subcategories_list:
                        subcategory_name = subcategory_data['name']
                        subcategory_description = subcategory_data.get('description', '')
                        
                        subcategory, created = Subcategory.objects.get_or_create(
                            name=subcategory_name,
                            category=category,
                            defaults={
                                'description': subcategory_description,
                                'is_deleted': False
                            }
                        )
                        
                        if created:
                            created_subcategories += 1
                            self.stdout.write(f'  Created subcategory: {subcategory_name}')
                        else:
                            # Обновляем описание, если подкатегория уже существует
                            if subcategory.description != subcategory_description:
                                subcategory.description = subcategory_description
                                subcategory.save(update_fields=['description'])
                                updated_subcategories += 1
                                self.stdout.write(f'  Updated description for: {subcategory_name}')

        # Выводим итоговую статистику
        self.stdout.write('')
        self.stdout.write(
            self.style.SUCCESS(
                f'Data loading completed:\n'
                f'- Created {created_categories} categories\n'
                f'- Created {created_subcategories} subcategories\n'
                f'- Updated {updated_subcategories} subcategory descriptions'
            )
        )
