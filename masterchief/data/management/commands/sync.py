from common.utils import cached_property
from data.importers import ClientDataImporter

from .bases import LabelCommand


class Command(LabelCommand):
    help = 'sync from client label command'

    def add_arguments(self, parser):
        super().add_arguments(parser)
        parser.add_argument(
            '-action', '--action', action='store', dest='action', default=None,
            help='Specify action param to sync during command action',
        )

    @cached_property
    def importer(self, *args, **kwargs):
        return self.get_importer(**kwargs)

    def get_importer(self, *args, **kwargs):
        return ClientDataImporter(**kwargs)

    def handle_label_vendors(self, *args, **options):
        """
            $ python manage.py sync vendors
        """
        self.importer.handle('import_vendors')

    def handle_label_categories(self, *args, **options):
        """
            $ python manage.py sync categories
        """
        self.importer.import_categories()

    def handle_label_products(self, *args, **options):
        """
            $ python manage.py sync products --vendor_id=<vendor_id>
        """
        assert options['vendor_id']

        if options['action'] == 'from_scratch':
            options['from_scratch'] = True
        self.get_importer(**options).handle('import_products')

    def handle_label_enabled_vendors_products(self, *args, **options):
        """
        will run sync products per every configured/enabled vendor

            $ python manage.py sync enabled_vendors_products
        """
        self.importer.handle_enabled_vendors_products(action=options['action'])
