from django.core.management.base import LabelCommand as <PERSON>LabelCommand, CommandError, BaseCommand
from django.core.management import call_command
from django.utils.functional import cached_property

from service_api_client import ServiceApiClient


class ServiceApiClientMixin:
    @cached_property
    def service_api_view(self):
        return ServiceApiClient()


class LabelCommand(CoreLabelCommand, ServiceApiClientMixin):
    """
    just extend your LabelCommands from that class,
    and then add methods names .handle_label_<your_labels>
    """

    def add_arguments(self, parser):
        super(LabelCommand, self).add_arguments(parser)
        parser.add_argument(
            '-vendor', '--vendor_id', action='store', dest='vendor_id', default=None,
            help='Specify vendor id to use for label command action',
        )

    def handle_label(self, label, *args, **options):
        self._get_label_method(label)(*args, **options)

    def _get_label_method(self, label):
        method = getattr(self, 'handle_label_{}'.format(label), None)
        if not method:
            raise CommandError('No method to handle "{}"'.format(label))
        return method
