from unittest.mock import Mock, patch
from django.test import TestCase

from data.importers import ClientDataImporter
from data.models import Vendor


class TestClientDataImporter(TestCase):
    def setUp(self):
        self.importer = ClientDataImporter()
        self.importer.init_stats()

    def test_handle_vendors_empty_list(self):
        """Test handling empty vendors list."""
        self.importer.handle_vendors([])
        assert self.importer.stats['vendors']['total_parsed'] == 0
        assert self.importer.stats['vendors']['found_existing'] == 0

    def test_handle_vendors_create_new(self):
        """Test creating new vendors."""
        vendor_data = [
            {'id': 1, 'name': 'Vendor 1', 'code': 'V1', 'settings': {}},
            {'id': 2, 'name': 'Vendor 2', 'code': 'V2', 'settings': {}}
        ]
        
        with patch.object(self.importer, '_get_existing_by_client_id') as mock_get_existing:
            mock_get_existing.return_value = {}
            with patch.object(self.importer, '_create_vendor') as mock_create:
                self.importer.handle_vendors(vendor_data)
                
                assert mock_create.call_count == 2
                assert self.importer.stats['vendors']['total_parsed'] == 2
                assert self.importer.stats['vendors']['found_existing'] == 0
                # assert self.importer.stats['vendors']['created'] == 2

    def test_handle_vendors_update_existing(self):
        """Test updating existing vendors."""
        vendor_data = [
            {'id': 1, 'name': 'Updated Vendor 1', 'code': 'V1', 'settings': {'new': 'setting'}},
            {'id': 2, 'name': 'Updated Vendor 2', 'code': 'V2', 'settings': {}}
        ]
        
        existing_vendors = {
            1: Mock(spec=Vendor),
            2: Mock(spec=Vendor)
        }
        
        with patch.object(self.importer, '_get_existing_by_client_id') as mock_get_existing:
            mock_get_existing.return_value = existing_vendors
            with patch.object(self.importer, '_update_vendor') as mock_update:
                mock_update.return_value = (Mock(), True)  # Return (vendor, was_updated)
                self.importer.handle_vendors(vendor_data)
                
                assert mock_update.call_count == 2
                assert self.importer.stats['vendors']['total_parsed'] == 2
                assert self.importer.stats['vendors']['found_existing'] == 2
                # assert self.importer.stats['vendors']['updated'] == 2

    def test_handle_vendors_mixed_operations(self):
        """Test handling mix of new and existing vendors."""
        vendor_data = [
            {'id': 1, 'name': 'Existing Vendor', 'code': 'V1', 'settings': {}},
            {'id': 2, 'name': 'New Vendor', 'code': 'V2', 'settings': {}}
        ]
        
        existing_vendors = {1: Mock(spec=Vendor)}
        
        with patch.object(self.importer, '_get_existing_by_client_id') as mock_get_existing:
            mock_get_existing.return_value = existing_vendors
            with patch.object(self.importer, '_update_vendor') as mock_update:
                mock_update.return_value = (Mock(), True)
                with patch.object(self.importer, '_create_vendor') as mock_create:
                    self.importer.handle_vendors(vendor_data)
                    
                    assert mock_update.call_count == 1
                    assert mock_create.call_count == 1
                    assert self.importer.stats['vendors']['total_parsed'] == 2
                    assert self.importer.stats['vendors']['found_existing'] == 1
                    # assert self.importer.stats['vendors']['created'] == 1
                    # assert self.importer.stats['vendors']['updated'] == 1

    def test_handle_vendors_calls_on_handle_vendors(self):
        """Test that on_handle_vendors is called after processing."""
        vendor_data = [{'id': 1, 'name': 'Vendor', 'code': 'V1', 'settings': {}}]
        
        with patch.object(self.importer, '_get_existing_by_client_id') as mock_get_existing:
            mock_get_existing.return_value = {}
            with patch.object(self.importer, '_create_vendor'):
                with patch.object(self.importer, 'on_handle_vendors') as mock_on_handle:
                    self.importer.handle_vendors(vendor_data)
                    mock_on_handle.assert_called_once()

    def test_handle_vendors_invalid_data(self):
        """Test handling invalid vendor data."""
        invalid_data = [
            {'id': 1},  # Missing required fields
            {'name': 'Vendor', 'code': 'V1'},  # Missing id
            None,  # None value
        ]
        
        with patch.object(self.importer, '_get_existing_by_client_id') as mock_get_existing:
            mock_get_existing.return_value = {}

            self.assertRaises(
                Exception,
                self.importer.handle_vendors,
                invalid_data
            )
