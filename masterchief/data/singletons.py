from masterchief.config import DAY_CACHE_TIMEOUT
from common.utils import get_cache_key, cache

VENDORS_BY_CODES, VENDORS_BY_IDS = None, None


def get_vendors_by_codes(cache_key='VENDORS_BY_CODES', cache_timeout=DAY_CACHE_TIMEOUT, reload=False):
    global VENDORS_BY_CODES, VENDORS_BY_IDS

    if VENDORS_BY_CODES is None or reload:
        cache_key = get_cache_key(cache_key)
        VENDORS_BY_CODES = cache.get(cache_key, None)
        if not VENDORS_BY_CODES or reload:
            from data.models import Vendor

            VENDORS_BY_CODES = {
                v.code: v for v in Vendor.objects.all()
            }
            VENDORS_BY_IDS = {
                v.id: v for v in VENDORS_BY_CODES.values()
            }
            cache.set(cache_key, VENDORS_BY_CODES, timeout=cache_timeout)

        if not VENDORS_BY_IDS:
            VENDORS_BY_IDS = {v.id: v for v in VENDORS_BY_CODES.values()}
    return VENDORS_BY_CODES


def get_vendors_by_ids(reload=False):
    global VENDORS_BY_IDS

    if not VENDORS_BY_IDS or reload:
        get_vendors_by_codes(reload=reload)
        assert VENDORS_BY_IDS
    return VENDORS_BY_IDS
