import json
import os
from django.db import migrations
from django.conf import settings


def load_categories_data(apps, schema_editor):
    """Загружает категории и подкатегории из categories.json"""
    Category = apps.get_model('data', 'Category')
    Subcategory = apps.get_model('data', 'Subcategory')

    # Путь к файлу categories.json
    categories_file_path = os.path.join(settings.BASE_DIR, 'masterchief', 'categories.json')

    if not os.path.exists(categories_file_path):
        print(f"Warning: categories.json not found at {categories_file_path}")
        return

    try:
        with open(categories_file_path, 'r', encoding='utf-8') as f:
            categories_data = json.load(f)
    except Exception as e:
        print(f"Error reading categories.json: {e}")
        return

    created_categories = 0
    created_subcategories = 0

    for category_item in categories_data:
        # Каждый элемент - это словарь с одним ключом (название категории)
        for category_name, subcategories_list in category_item.items():
            # Создаем или получаем категорию
            category, created = Category.objects.get_or_create(
                name=category_name,
                defaults={'is_deleted': False}
            )

            if created:
                created_categories += 1
                print(f"Created category: {category_name}")

            # Создаем подкатегории
            for subcategory_data in subcategories_list:
                subcategory_name = subcategory_data['name']
                subcategory_description = subcategory_data.get('description', '')

                subcategory, created = Subcategory.objects.get_or_create(
                    name=subcategory_name,
                    category=category,
                    defaults={
                        'description': subcategory_description,
                        'is_deleted': False
                    }
                )

                if created:
                    created_subcategories += 1
                    print(f"Created subcategory: {category_name} > {subcategory_name}")
                else:
                    # Обновляем описание, если подкатегория уже существует
                    if subcategory.description != subcategory_description:
                        subcategory.description = subcategory_description
                        subcategory.save(update_fields=['description'])
                        print(f"Updated description for: {category_name} > {subcategory_name}")

    print(f"Data loading completed:")
    print(f"- Created {created_categories} categories")
    print(f"- Created {created_subcategories} subcategories")


def reverse_load_categories_data(apps, schema_editor):
    """Обратная операция - удаляет все категории и подкатегории"""
    Category = apps.get_model('data', 'Category')
    Subcategory = apps.get_model('data', 'Subcategory')

    # Удаляем все подкатегории
    subcategories_count = Subcategory.objects.count()
    Subcategory.objects.all().delete()

    # Удаляем все категории
    categories_count = Category.objects.count()
    Category.objects.all().delete()

    print(f"Removed {subcategories_count} subcategories and {categories_count} categories")


class Migration(migrations.Migration):
    dependencies = [
        ('data', '0003_subcategory_description'),
    ]

    operations = [
        migrations.RunPython(
            load_categories_data,
            reverse_load_categories_data,
            elidable=True,
        ),
    ]
