# Generated by Django 5.2 on 2025-05-07 12:09

import data.models.structure
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Vendor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('dt_created', models.DateTimeField(auto_now_add=True, verbose_name='Дата добавления')),
                ('dt_updated', models.DateTimeField(auto_now=True, verbose_name='Дата обновления')),
                ('is_deleted', models.BooleanField(default=False)),
                ('name', models.CharField(max_length=64, verbose_name='Название')),
                ('code', models.CharField(blank=True, max_length=16, null=True, verbose_name='Код')),
                ('settings', models.JSONField(blank=True, default=data.models.structure.get_default_vendor_settings, verbose_name='Настройки')),
                ('params', models.JSONField(blank=True, default=data.models.structure.get_default_vendor_params, verbose_name='Параметры')),
                ('dt_synced', models.DateTimeField(null=True, verbose_name='Дата синхронизации')),
            ],
            options={
                'verbose_name': 'Вендор',
                'verbose_name_plural': 'Вендоры',
                'ordering': ['-id'],
            },
            bases=(models.Model, data.models.structure.VendorCodeCacheMixin),
        ),
        migrations.CreateModel(
            name='Settings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('dt_created', models.DateTimeField(auto_now_add=True, verbose_name='Дата добавления')),
                ('dt_updated', models.DateTimeField(auto_now=True, verbose_name='Дата обновления')),
                ('is_deleted', models.BooleanField(default=False)),
                ('name', models.CharField(db_index=True, default='default', max_length=32, unique=True, verbose_name='Уникальный идентификатор')),
                ('settings', models.JSONField(default=data.models.structure.get_default_settings)),
                ('changer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Глобальные настройки',
                'verbose_name_plural': 'Глобальные настройки',
                'ordering': ['-id'],
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('dt_created', models.DateTimeField(auto_now_add=True, verbose_name='Дата добавления')),
                ('dt_updated', models.DateTimeField(auto_now=True, verbose_name='Дата обновления')),
                ('is_deleted', models.BooleanField(default=False)),
                ('name', models.CharField(max_length=64, verbose_name='Название')),
                ('data', models.JSONField(blank=True, default=data.models.structure.get_default_product_data, verbose_name='Данные')),
                ('vendor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='products', to='data.vendor')),
            ],
            options={
                'verbose_name': 'Товар вендора',
                'verbose_name_plural': 'Товары вендора',
                'ordering': ['-id'],
            },
        ),
    ]
