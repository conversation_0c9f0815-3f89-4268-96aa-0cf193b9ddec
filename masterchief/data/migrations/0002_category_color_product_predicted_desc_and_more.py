# Generated by Django 5.2 on 2025-05-27 12:52

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("data", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Category",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "dt_created",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="Дата добавления"
                    ),
                ),
                (
                    "dt_updated",
                    models.DateTimeField(auto_now=True, verbose_name="Дата обновления"),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("name", models.CharField(max_length=64, verbose_name="Название")),
            ],
            options={
                "verbose_name": "Категория",
                "verbose_name_plural": "Категории",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="Color",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "dt_created",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="Дата добавления"
                    ),
                ),
                (
                    "dt_updated",
                    models.DateTimeField(auto_now=True, verbose_name="Дата обновления"),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("name", models.CharField(max_length=64, verbose_name="Название")),
                (
                    "hex_code",
                    models.CharField(
                        blank=True,
                        max_length=7,
                        null=True,
                        verbose_name="HEX код цвета",
                    ),
                ),
            ],
            options={
                "verbose_name": "Цвет",
                "verbose_name_plural": "Цвета",
                "ordering": ["name"],
            },
        ),
        migrations.AddField(
            model_name="product",
            name="predicted_desc",
            field=models.TextField(
                blank=True, null=True, verbose_name="Предсказанное описание"
            ),
        ),
        migrations.AddField(
            model_name="product",
            name="predicted_category",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="predicted_products",
                to="data.category",
                verbose_name="Предсказанная категория",
            ),
        ),
        migrations.AddField(
            model_name="product",
            name="predicted_color",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="predicted_products",
                to="data.color",
                verbose_name="Предсказанный цвет",
            ),
        ),
        migrations.CreateModel(
            name="Subcategory",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "dt_created",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="Дата добавления"
                    ),
                ),
                (
                    "dt_updated",
                    models.DateTimeField(auto_now=True, verbose_name="Дата обновления"),
                ),
                ("is_deleted", models.BooleanField(default=False)),
                ("name", models.CharField(max_length=64, verbose_name="Название")),
                (
                    "category",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="subcategories",
                        to="data.category",
                        verbose_name="Категория",
                    ),
                ),
            ],
            options={
                "verbose_name": "Подкатегория",
                "verbose_name_plural": "Подкатегории",
                "ordering": ["category__name", "name"],
                "unique_together": {("category", "name")},
            },
        ),
        migrations.AddField(
            model_name="product",
            name="predicted_subcategory",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="predicted_products",
                to="data.subcategory",
                verbose_name="Предсказанная подкатегория",
            ),
        ),
    ]
