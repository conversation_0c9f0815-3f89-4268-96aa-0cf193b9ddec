from common import capture_message, logger
from masterchief.tasks.bases import *


@shared_task(base=Task, bind=True)
def task_sync_vendors(self, *args, **kwargs):
    """
    Sync vendors, then sync products
    """
    logger.info('Task sync_vendors is now processing')
    call_django_command('sync', 'vendors')


@shared_task(base=Task, bind=True)
def task_sync_enabled_vendors_products(self, *args, **kwargs):
    """
    Sync products for all enabled vendors (atm all?)
    """
    logger.info('Task enabled_vendors_products is now processing')
    call_django_command('sync', 'enabled_vendors_products')


@shared_task(base=Task)
def task_sunday_cleanup():
    capture_message('Sunday cleanup is handling...')
