import json

from celery import shared_task
from django.conf import settings


class ExampleSharedTasksClass:
    @staticmethod
    @shared_task()
    def publish_look_to_pubsub_topic(look_id: int):
        """
        A task that exports a look to a pubsub topic
        """
        pass

    @staticmethod
    @shared_task()
    def import_looks(import_data: dict):
        """
        A task that imports one or more looks
        """
        pass
