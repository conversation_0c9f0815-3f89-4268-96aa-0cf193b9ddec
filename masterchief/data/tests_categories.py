import json
import tempfile
import os
from django.test import TestCase
from django.core.management import call_command
from django.core.management.base import CommandError
from io import StringIO

from data.models import Category, Subcategory


class LoadCategoriesCommandTestCase(TestCase):
    def setUp(self):
        # Создаем тестовые данные
        self.test_data = [
            {
                "Тестовая категория 1": [
                    {
                        "name": "Подкатегория 1.1",
                        "description": "Описание подкатегории 1.1"
                    },
                    {
                        "name": "Подкатегория 1.2",
                        "description": "Описание подкатегории 1.2"
                    }
                ]
            },
            {
                "Тестовая категория 2": [
                    {
                        "name": "Подкатегория 2.1",
                        "description": "Описание подкатегории 2.1"
                    }
                ]
            }
        ]
    
    def create_temp_json_file(self, data):
        """Создает временный JSON файл с тестовыми данными"""
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        json.dump(data, temp_file, ensure_ascii=False, indent=2)
        temp_file.close()
        return temp_file.name
    
    def test_load_categories_success(self):
        """Тест успешной загрузки категорий"""
        temp_file = self.create_temp_json_file(self.test_data)
        
        try:
            out = StringIO()
            call_command('load_categories', file=temp_file, stdout=out)
            
            # Проверяем, что категории созданы
            self.assertEqual(Category.objects.count(), 2)
            self.assertEqual(Subcategory.objects.count(), 3)
            
            # Проверяем конкретные категории
            cat1 = Category.objects.get(name="Тестовая категория 1")
            cat2 = Category.objects.get(name="Тестовая категория 2")
            
            self.assertEqual(cat1.subcategories.count(), 2)
            self.assertEqual(cat2.subcategories.count(), 1)
            
            # Проверяем подкатегории с описаниями
            subcat1_1 = Subcategory.objects.get(name="Подкатегория 1.1", category=cat1)
            self.assertEqual(subcat1_1.description, "Описание подкатегории 1.1")
            
            subcat2_1 = Subcategory.objects.get(name="Подкатегория 2.1", category=cat2)
            self.assertEqual(subcat2_1.description, "Описание подкатегории 2.1")
            
        finally:
            os.unlink(temp_file)
    
    def test_load_categories_dry_run(self):
        """Тест dry-run режима"""
        temp_file = self.create_temp_json_file(self.test_data)
        
        try:
            out = StringIO()
            call_command('load_categories', file=temp_file, dry_run=True, stdout=out)
            
            # Проверяем, что ничего не создано
            self.assertEqual(Category.objects.count(), 0)
            self.assertEqual(Subcategory.objects.count(), 0)
            
            # Проверяем, что в выводе есть информация о данных
            output = out.getvalue()
            self.assertIn('DRY RUN', output)
            self.assertIn('Found 2 categories and 3 subcategories', output)
            
        finally:
            os.unlink(temp_file)
    
    def test_load_categories_clear_existing(self):
        """Тест очистки существующих данных"""
        # Создаем существующие данные
        existing_cat = Category.objects.create(name="Существующая категория")
        Subcategory.objects.create(
            name="Существующая подкатегория", 
            category=existing_cat,
            description="Старое описание"
        )
        
        temp_file = self.create_temp_json_file(self.test_data)
        
        try:
            out = StringIO()
            call_command('load_categories', file=temp_file, clear=True, stdout=out)
            
            # Проверяем, что старые данные удалены, новые созданы
            self.assertEqual(Category.objects.count(), 2)
            self.assertEqual(Subcategory.objects.count(), 3)
            
            # Проверяем, что старой категории нет
            self.assertFalse(Category.objects.filter(name="Существующая категория").exists())
            
        finally:
            os.unlink(temp_file)
    
    def test_load_categories_update_description(self):
        """Тест обновления описания существующих подкатегорий"""
        # Создаем существующие данные
        cat = Category.objects.create(name="Тестовая категория 1")
        subcat = Subcategory.objects.create(
            name="Подкатегория 1.1",
            category=cat,
            description="Старое описание"
        )
        
        temp_file = self.create_temp_json_file(self.test_data)
        
        try:
            out = StringIO()
            call_command('load_categories', file=temp_file, stdout=out)
            
            # Проверяем, что описание обновилось
            subcat.refresh_from_db()
            self.assertEqual(subcat.description, "Описание подкатегории 1.1")
            
            # Проверяем, что в выводе есть информация об обновлении
            output = out.getvalue()
            self.assertIn('Updated description for: Подкатегория 1.1', output)
            
        finally:
            os.unlink(temp_file)
    
    def test_load_categories_file_not_found(self):
        """Тест обработки отсутствующего файла"""
        with self.assertRaises(CommandError) as cm:
            call_command('load_categories', file='nonexistent.json')
        
        self.assertIn('Categories file not found', str(cm.exception))
    
    def test_load_categories_invalid_json(self):
        """Тест обработки невалидного JSON"""
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        temp_file.write('invalid json content')
        temp_file.close()
        
        try:
            with self.assertRaises(CommandError) as cm:
                call_command('load_categories', file=temp_file.name)
            
            self.assertIn('Error reading categories file', str(cm.exception))
            
        finally:
            os.unlink(temp_file.name)


class CategoryModelTestCase(TestCase):
    def test_category_str(self):
        """Тест строкового представления категории"""
        category = Category.objects.create(name="Тестовая категория")
        self.assertEqual(str(category), "Тестовая категория")
    
    def test_subcategory_str(self):
        """Тест строкового представления подкатегории"""
        category = Category.objects.create(name="Категория")
        subcategory = Subcategory.objects.create(
            name="Подкатегория",
            category=category,
            description="Описание"
        )
        self.assertEqual(str(subcategory), "Категория > Подкатегория")
    
    def test_subcategory_unique_together(self):
        """Тест уникальности пары (category, name) для подкатегории"""
        category = Category.objects.create(name="Категория")
        
        # Создаем первую подкатегорию
        Subcategory.objects.create(
            name="Подкатегория",
            category=category,
            description="Описание 1"
        )
        
        # Попытка создать подкатегорию с тем же именем в той же категории должна вызвать ошибку
        from django.db import IntegrityError
        with self.assertRaises(IntegrityError):
            Subcategory.objects.create(
                name="Подкатегория",
                category=category,
                description="Описание 2"
            )
    
    def test_subcategory_same_name_different_categories(self):
        """Тест возможности создания подкатегорий с одинаковыми именами в разных категориях"""
        cat1 = Category.objects.create(name="Категория 1")
        cat2 = Category.objects.create(name="Категория 2")
        
        # Создаем подкатегории с одинаковыми именами в разных категориях
        subcat1 = Subcategory.objects.create(
            name="Подкатегория",
            category=cat1,
            description="Описание 1"
        )
        
        subcat2 = Subcategory.objects.create(
            name="Подкатегория",
            category=cat2,
            description="Описание 2"
        )
        
        self.assertEqual(subcat1.name, subcat2.name)
        self.assertNotEqual(subcat1.category, subcat2.category)
