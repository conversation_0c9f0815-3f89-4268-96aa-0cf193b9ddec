from django.db import models
from django.urls import reverse_lazy

from django.utils.functional import classproperty, cached_property
from django.utils.translation import gettext_lazy as _

from django.contrib.auth import get_user_model

from common.utils import get_current_host_url

User = get_user_model()


class ActiveOnlyManager(models.Manager):
    use_for_related_fields = True

    def get_queryset(self, *args, **kwargs):
        qs = super(ActiveOnlyManager, self).get_queryset(*args, **kwargs)
        return qs.exclude(is_deleted=True)


class CreatedUpdatedModelMixin(models.Model):
    dt_created = models.DateTimeField(auto_now_add=True, verbose_name=_('Дата добавления'))
    dt_updated = models.DateTimeField(auto_now=True, verbose_name=_('Дата обновления'))

    class Meta:
        abstract = True

    @property
    def prop_cache_key_part(self):
        # note: can add class name: {self.__class__.__name__}_
        # TODO: split dt_updated by '.'?
        return f'{self.pk}_{self.updated_or_created}'

    @property
    def updated_or_created(self):
        return self.dt_updated or self.dt_created or ''

    def _get_dt_str(self, dt):
        if not dt:
            return '-'
        return str(dt).split('.', 1)[0]

    def get_dates_str(self):
        dates = [self._get_dt_str(self.dt_created)]
        str_dt_updated = self._get_dt_str(self.dt_updated)
        if str_dt_updated != dates[-1]:
            dates.append(str_dt_updated)
        return ' / '.join(dates)

    @cached_property
    def dt_created_updated(self):
        return self.get_dates_str()


class BaseModel(CreatedUpdatedModelMixin):
    is_deleted = models.BooleanField(default=False)
    # managers
    objects = models.Manager()
    active_objects = ActiveOnlyManager()

    @classproperty
    def all_manager(cls):
        return getattr(cls, 'objects', None) or cls._default_manager

    @classmethod
    def by_id(cls, oid):
        return cls.all_manager.filter(id=oid).first()

    @classproperty
    def run_task(cls):
        pass

    class Meta:
        abstract = True


class NamedBaseModel(BaseModel):
    name = models.CharField(max_length=64, verbose_name=_('Название'))

    class Meta:
        abstract = True

    def __str__(self):
        return self.name or ''

    def get_absolute_admin_url(self, with_host=True):
        app_label = self._meta.app_label
        url_path = f'admin:{app_label}_{self.__class__.__name__}_change'.lower()
        url = reverse_lazy(url_path, args=[self.pk])
        if url:
            url = get_current_host_url(url)
        return url

    def deactivate(self, **kwargs):
        kwargs['is_deleted'] = True
        for k, v in kwargs.items():
            setattr(self, k, v)
        self.save(update_fields=list(kwargs.keys()))

    def activate(self):
        self.is_deleted = False
        self.save(update_fields=['is_deleted'])

    @classmethod
    def tune_qs(cls, qs, prefetch_related=False, select_related=False, active_only=None):
        if active_only:
            # qs = qs.exclude(is_deleted=True)
            # Note: 20210809, this line below with .filter seems faster then .exclude
            qs = qs.filter(is_deleted=False)

        if prefetch_related:
            if isinstance(prefetch_related, str):
                prefetch_related = [prefetch_related]
            qs = qs.prefetch_related(*prefetch_related)
        if select_related:
            if isinstance(select_related, str):
                select_related = [select_related]
            qs = qs.select_related(*select_related)
        return qs


class ClientIdObjectMixin:
    client_id = models.BigIntegerField(verbose_name=_('client-server object ID'), unique=True)


class EnabledModelMixin(models.Model):
    is_enabled = models.BooleanField(default=True, verbose_name=_('Включено'))

    class Meta:
        abstract = True
