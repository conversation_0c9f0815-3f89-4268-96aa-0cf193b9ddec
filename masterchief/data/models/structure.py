from functools import cached_property

from django.db import models
from django.utils.translation import gettext_lazy as _

from common.bases import CommonCachedObjectMixin
from data.singletons import get_vendors_by_codes, get_vendors_by_ids
from .bases import NamedBaseModel, User, ClientIdObjectMixin


DEFAULT_SETTINGS = {}
DEFAULT_VENDOR_SETTINGS = {}
DEFAULT_VENDOR_PARAMS = {}
DEFAULT_PRODUCT_DATA = {}

def get_default_settings():
    return DEFAULT_SETTINGS.copy()


def get_default_vendor_settings():
    return DEFAULT_VENDOR_SETTINGS.copy()


def get_default_vendor_params():
    return DEFAULT_VENDOR_PARAMS.copy()


def get_default_product_data():
    return DEFAULT_PRODUCT_DATA.copy()


VENDORS_CODES_BY_ID = None

class VendorCodeCacheMixin:
    @classmethod
    def get_vendor_code_by_vendor_id(cls, vendor_id, reload=False):
        global VENDORS_CODES_BY_ID

        if VENDORS_CODES_BY_ID is None:
            VENDORS_CODES_BY_ID = {v.id: v.code for v in get_vendors_by_ids(reload=reload).values()}

        if vendor_id not in VENDORS_CODES_BY_ID and not reload:
            return cls.get_vendor_code_by_vendor_id(vendor_id, reload=True)

        return VENDORS_CODES_BY_ID.get(vendor_id)

    @staticmethod
    def get_vendors_by_codes(reload=False):
        return get_vendors_by_codes(reload=reload)

    @staticmethod
    def get_vendors_by_ids(reload=False):
        return get_vendors_by_ids(reload=reload)

    @classmethod
    def get_vendor_by_code(cls, vendor_code, reload=False):
        return cls.get_vendors_by_codes(reload=reload).get(vendor_code)

    @classmethod
    def get_vendor_by_id(cls, vendor_id, reload=False):
        return cls.get_vendors_by_ids(reload=reload).get(int(vendor_id))

    @classmethod
    def get_vendor_by_id_or_code(cls, vendor_id=None, vendor=None, raise_exception=False, reload=False):
        """
        vendor_id can be vendor code here
        """
        if not vendor and vendor_id:
            if isinstance(vendor_id, int) or vendor_id.isdigit():
                vendor = cls.get_vendor_by_id(vendor_id, reload=reload)
            else:
                # assuming vendor_id is str, vendor code
                vendor = cls.get_vendor_by_code(vendor_id, reload=reload)
        elif vendor:
            assert isinstance(vendor, Vendor)

        if not vendor and raise_exception:
            raise Exception('No vendor for vendor_id(or code)={}'.format(vendor_id))
        return vendor


# Vendor aka Company
class Vendor(NamedBaseModel, VendorCodeCacheMixin):
    code = models.CharField(max_length=16, verbose_name=_('Код'), null=True, blank=True)
    settings = models.JSONField(default=get_default_vendor_settings, verbose_name=_('Настройки'), blank=True)
    params = models.JSONField(default=get_default_vendor_params, verbose_name=_('Параметры'), blank=True)

    dt_synced = models.DateTimeField(null=True, verbose_name=_('Дата синхронизации'))

    class Meta:
        ordering = ['-id']
        verbose_name = _('Вендор')
        verbose_name_plural = _('Вендоры')

    def __str__(self):
        return f'#{self.id} {self.name}'

    @classmethod
    def get_env_active_vendors_queryset(cls, **kwargs):
        return Vendor.active_objects.all()

    def get_products_query(self, active_only=False, prefetch_related=None, select_related=None):
        qs = self.products.all()
        qs = self.tune_qs(qs, active_only=active_only,
                          prefetch_related=prefetch_related, select_related=select_related)
        return qs

    @property
    def vendor_params(self):
        return (self.params or {})

    @property
    def prop_is_enabled_for_sync(self):
        return self.vendor_params.get('is_enabled_for_sync', False)

    @property
    def prop_sync_with_tags(self):
        return False

    @property
    def prop_sync_active_only(self):
        return False

    def get_latest_updated_created_date(self):
        product_updated = self.products.order_by('-dt_updated', '-dt_created').first()
        if product_updated:
            dates = []
            dates.append(product_updated.dt_created)
            if product_updated.dt_updated:
                dates.append(product_updated.dt_updated)
            if dates:
                return max(*dates)
        return None

    @property
    def prop_dt_synced(self):
        if self.dt_synced:
            return self.dt_synced
        latest = self.get_latest_updated_created_date()
        if latest:
            # auto-set
            self.prop_dt_synced = latest
            return latest
        return None

    @prop_dt_synced.setter
    def prop_dt_synced(self, dt_synced):
        self.dt_synced = dt_synced
        self.save(update_fields=['dt_synced', 'dt_updated'])


class Settings(NamedBaseModel, CommonCachedObjectMixin):
    name = models.CharField(
        default='default', max_length=32, unique=True, db_index=True, verbose_name=_('Уникальный идентификатор'))
    changer = models.ForeignKey(User, null=True, blank=True, on_delete=models.SET_NULL)
    settings = models.JSONField(default=get_default_settings)

    class Meta:
        ordering = ['-id']
        verbose_name = _('Глобальные настройки')
        verbose_name_plural = _('Глобальные настройки')

    def __str__(self):
        return '{} #{}, is active: {}'.format(self._meta.verbose_name, self.id, not self.is_deleted)

    def _validate_settings(self):
        self.settings = self.settings or {}
        # ADD SOME ADDON VALIDATION
        pass

    def save(self, *args, **kwargs):
        self._validate_settings()
        super().save(*args, **kwargs)
        self.update_obj_cache()

    def update_obj_cache(self):
        self.obj_cache_set(self.name)
        self.obj_cache_set(self.name + '_dt_updated', self.dt_updated)

    @property
    def obj_cache_should_be_refreshed(self):
        dt_updated = self.obj_cache_get(self.name + '_dt_updated', default=None)
        if not dt_updated or self.dt_updated < dt_updated:
            return True
        return False

    @classmethod
    def get_or_create_named_settings(cls, name, settings=None, use_cache=True, **defaults):
        is_created = False
        obj = None
        if use_cache:
            obj = cls.obj_cache_get(name)
        if not obj:
            obj = cls.objects.filter(name=name).first()
            if not obj:
                obj = cls(
                    name=name, settings=settings or {}, **defaults
                )
                obj.save()  # .update_obj_cache is called during .save, so "store to obj cache" too
                is_created = True
            else:
                obj.update_obj_cache()  # got from db, now "store to obj cache" for future re-use from cache
        else:
            # got from obj_cache (aka .common_cache)! TODO: check actualization/.dt_updated?
            pass
        return obj, is_created


class Category(NamedBaseModel):
    """Категория товаров"""

    class Meta:
        ordering = ['name']
        verbose_name = _('Категория')
        verbose_name_plural = _('Категории')


class Subcategory(NamedBaseModel):
    """Подкатегория товаров"""
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='subcategories', verbose_name=_('Категория'))
    description = models.TextField(blank=True, verbose_name=_('Описание'))

    class Meta:
        ordering = ['category__name', 'name']
        verbose_name = _('Подкатегория')
        verbose_name_plural = _('Подкатегории')
        unique_together = ['category', 'name']

    def __str__(self):
        return f'{self.category.name} > {self.name}'


class Color(NamedBaseModel):
    """Цвет товара"""
    hex_code = models.CharField(max_length=7, null=True, blank=True, verbose_name=_('HEX код цвета'))

    class Meta:
        ordering = ['name']
        verbose_name = _('Цвет')
        verbose_name_plural = _('Цвета')


class Product(NamedBaseModel):
    vendor = models.ForeignKey(Vendor, on_delete=models.CASCADE, related_name='products')
    data = models.JSONField(default=get_default_product_data, verbose_name=_('Данные'), blank=True)

    # AI-predicted fields
    predicted_category = models.ForeignKey(Category, null=True, blank=True, on_delete=models.SET_NULL,
                                         related_name='predicted_products', verbose_name=_('Предсказанная категория'))
    predicted_subcategory = models.ForeignKey(Subcategory, null=True, blank=True, on_delete=models.SET_NULL,
                                            related_name='predicted_products', verbose_name=_('Предсказанная подкатегория'))
    predicted_color = models.ForeignKey(Color, null=True, blank=True, on_delete=models.SET_NULL,
                                      related_name='predicted_products', verbose_name=_('Предсказанный цвет'))
    predicted_desc = models.TextField(null=True, blank=True, verbose_name=_('Предсказанное описание'))

    class Meta:
        ordering = ['-id']
        verbose_name = _('Товар вендора')
        verbose_name_plural = _('Товары вендора')

    @cached_property
    def prop_vendor(self):
        return self.vendor
