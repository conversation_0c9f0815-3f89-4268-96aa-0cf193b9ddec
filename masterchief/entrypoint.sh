#!/bin/bash

set -e

# Wait for the database to be ready
echo "Waiting for database at $DB_HOST:$DB_PORT..."
for i in {1..30}; do
  pg_isready -h $DB_HOST -p $DB_PORT -U $DB_USER && echo "Database is ready!" && break
  echo "Waiting for database... ($i/30)"
  sleep 2
done

# Consider, should we do it(migrate/collectstatic) every time we run app? probably, on deploy only, so, atm commented
## Apply database migrations
#echo "Applying database migrations..."
#python manage.py migrate
#
## Collect static files
#echo "Collecting static files..."
#python manage.py collectstatic --noinput


echo "Starting celery..."
/celery.sh start

echo "Docker ENV is copied to .env..."
cat .env.docker > .env

# Start server
echo "Starting server..."
exec "$@"
