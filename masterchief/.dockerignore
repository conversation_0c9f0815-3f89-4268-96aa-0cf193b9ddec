# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-*
media

# Docker
.dockerignore
Dockerfile
docker-compose.yml
docker-compose.yaml

# Git
.git
.gitignore

# IDE
.idea/
.vscode/
*.swp
*.swo

# Environment
.env
# Allow Docker-specific env file
!.env.docker
.venv
env/
venv/
ENV/
logs/*

# Node
node_modules/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db 