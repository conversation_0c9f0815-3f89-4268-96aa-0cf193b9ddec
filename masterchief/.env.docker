# Django settings
DEBUG=True
# to be considered
ALLOWED_HOSTS=localhost,127.0.0.1
DJANGO_SETTINGS_MODULE=masterchief.settings

# Dont forget to configure, also, microservices fast-api should use same
# CLIENT_SERVICE_API_TOKEN=<should be same token as from client-server>

# Database settings, Note: this is valid configuration for using db from (Developer local)host postgres
# note also network_mode: "host" in docker-compose.yml
DB_ENGINE=django.db.backends.postgresql
DB_NAME=masterchief
DB_USER=masterchief
DB_PASSWORD=masterchief
DB_HOST=localhost
DB_PORT=5434

# Static and media files
STATIC_URL=/static/
MEDIA_URL=/media/

# docker app should be actually using masterchief-redis:6380, started with docker-compose
# redis.conf port 6380 is configured and mapped to same on host
REDIS_URI=redis://localhost:6380/15