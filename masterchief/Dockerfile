FROM python:3.11-slim

WORKDIR /app

# Install system dependencies including PostgreSQL development files
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    libpq-dev \
    postgresql-client \
    procps \
    && rm -rf /var/lib/apt/lists/*

# Install gunicorn for ASGI application
RUN pip install --no-cache-dir gunicorn uvicorn

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy project files
COPY . .

# Create static files directory
# RUN mkdir -p /app/staticfiles && chmod -R 755 /app/staticfiles

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DJANGO_SETTINGS_MODULE=masterchief.settings

# Add a healthcheck to verify the application is running
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8000/ || exit 1

# Expose the port the app runs on
EXPOSE 8000

# Set entrypoint
COPY entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh


# Copy env file
# warning: docker uses db settings from host hosted, that is temporary approach anyway
# should be git-based ENV settings on deployment

# Warning: that is not working
# COPY .env.docker .env
# so, we are using masterchief/entrypoint.sh:27 to copy docker specific env in .env
# Anyway, on production, we should use $ENV from gitlab project variables

# Copy celery script
RUN groupadd -r celery && useradd -r -g celery celery
RUN chmod o+wx /app/logs
COPY celery.sh /celery.sh
RUN chmod +x /celery.sh

# celery.sh should be run in enprypoint?
ENTRYPOINT ["/entrypoint.sh"]


# On demand, add more verbose logging for Gunicorn
# ENV GUNICORN_CMD_ARGS="--log-level=debug"

# Command to run the application using Gunicorn with Uvicorn workers for ASGI
CMD ["gunicorn", "masterchief.asgi:application", "--bind", "0.0.0.0:8000", "--workers", "4", "--worker-class", "uvicorn.workers.UvicornWorker"]