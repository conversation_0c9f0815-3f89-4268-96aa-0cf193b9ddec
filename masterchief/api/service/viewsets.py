from collections import defaultdict
from pprint import pprint

from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import authentication, exceptions, status
from rest_framework.permissions import BasePermission
from rest_framework import viewsets  # noqa

from django.utils.functional import cached_property
from django.core.paginator import Paginator
from django.db.models import Q

from common.logging import capture_message
from masterchief.config import X_API_TOKEN
from data.models import Vendor, Product, Settings
from common.utils import parse_dt, str_dt


class BaseViewSet(viewsets.ViewSet):
    def response(self, error=None, code=0, result_status=None, **additional_data):
        result = 'ok'
        data = {}
        if error:
            data['error'] = error
            if not code and result_status:
                code = result_status
            result = 'not_ok'
            if not code:
                code = 500
        else:
            pass
        data.update({'result': result, 'code': code})
        data.update(additional_data)
        return Response(data, status=result_status)


SECRET_TOKENS_ALLOWED = []


class SecretTokenAuthentication(authentication.BaseAuthentication):
    secret_tokens_configured = []

    @cached_property
    def secret_tokens_allowed(self):
        return SECRET_TOKENS_ALLOWED + self.secret_tokens_configured

    def authenticate(self, request):
        error = None
        data = request and request.data
        secret_token = request.headers.get('X_API_TOKEN') or \
                       (data.get('api_token') if data and isinstance(data, dict) else None)
        if self.secret_tokens_allowed:
            if not secret_token or secret_token not in self.secret_tokens_allowed:
                error = 'Token is not provided or not in allowed tokens'
        request.authentication_error = error
        return None, error


class ExportViewSecretTokenAuthentication(SecretTokenAuthentication):
    secret_tokens_configured = [X_API_TOKEN]

    def authenticate(self, request):
        # 2025.04.07, remove to enable when we are ready to switch it on in production
        # external systems should support fetching of data
        # proposed client api class to use: retail.api.service.export_service_api_client.ServiceApiClient
        # request.authentication_error = 'Disabled endpoint'
        # return None, 'Disabled endpoint'

        error = None
        data = request and request.data
        secret_token = request.headers.get('X_API_TOKEN') or \
                       (data.get('api_token') if data and isinstance(data, dict) else None)
        if self.secret_tokens_allowed:
            if not secret_token or secret_token not in self.secret_tokens_allowed:
                error = 'Token is not provided or not in allowed tokens'
        request.authentication_error = error
        return None, error


class ExportViewSet(BaseViewSet):
    """
    draft:

    экспорт данных во внешние системы:
    - Vector Hub
    - looks generator
    - etc...

    /export/vendors
    /export/categories
    /export/products
    """

    # Note: that not working, this is used for request.user fill/authentification
    authentication_classes = [ExportViewSecretTokenAuthentication]

    page_size_products = 100

    @action(['post', 'get'], detail=False, url_path='service_settings', url_name='service_settings')
    def export_service_settings(self, request, *args, **kwargs):
        return self._do_export(request, export_func=self._do_export_service_settings, *args, **kwargs)

    @action(['post', 'get'], detail=False, url_path='on_last_chunk', url_name='on_last_chunk')
    def export_on_last_chunk(self, request, *args, **kwargs):
        return self._do_export(request, export_func=self._do_export_on_last_chunk, *args, **kwargs)

    @action(['post', 'get'], detail=False, url_path='vendors', url_name='export_vendors')
    def export_vendors(self, request, *args, **kwargs):
        return self._do_export(request, export_func=self._do_export_vendors, *args, **kwargs)

    @action(['post', 'get'], detail=False, url_path='categories', url_name='export_categories')
    def export_categories(self, request, *args, **kwargs):
        return self._do_export(request, export_func=self._do_export_categories, *args, **kwargs)

    @action(['post', 'get'], detail=False, url_path='products', url_name='export_products')
    def export_products(self, request, *args, **kwargs):
        return self._do_export(request, export_func=self._do_export_products, *args, **kwargs)

    def update_service__dt_latest(self, service_name, dt_latest):
        settings_obj, created = Settings.get_or_create_named_settings(service_name)
        settings_obj.settings = settings_obj.settings or {}
        settings_obj.settings['dt_latest'] = dt_latest
        settings_obj.save()

    def _do_export_service_settings(self, request, *args, **kwargs):
        service_name = request.data.get('service_name')
        settings_obj, created = Settings.get_or_create_named_settings(service_name)
        settings = settings_obj.settings or {}

        result, error = settings.copy(), False
        return result, error

    def _do_export_on_last_chunk(self, request, *args, **kwargs):
        service_name = request.data.get('service_name')
        dt_latest = request.data.get('dt_latest')
        if service_name and dt_latest:
            self.update_service__dt_latest(service_name, dt_latest)
        result, error = {'ok': 1}, False
        return result, error

    def _get_vendors_to_export(request, *args, **kwargs):
        return Vendor.get_env_active_vendors_queryset().order_by('id')

    def _serialize_vendor(self, vendor):
        return {
            'id': vendor.id,
            'code': vendor.code,
            'settings': {}
        }

    def _do_export_vendors(self, request, *args, **kwargs):
        vendors = []
        result, error = {'vendors': vendors}, False
        vendors_qs = self._get_vendors_to_export(request, *args, **kwargs)
        # TODO: better drf-based serialization?
        for vendor in vendors_qs:
            vendors.append(self._serialize_vendor(vendor))
        return result, error

    def _serialize_category(self, category):
        is_cloth = False
        cg_info, cg = None, category.prop_category_group
        if cg:
            is_cloth = cg.is_cloth_category_group
            cg_info = {
                'id': cg.id,
                'name': cg.name,
                'code': cg.code,
            }
        root_id = category.root_category_id
        if category.id == root_id:
            root_id = None
        return {
            'id': category.id,
            'root_id': root_id,
            'full_name': category.full_name,
            'category_group': cg_info,
            'is_cloth': is_cloth
        }

    def _do_export_categories(self, request, *args, **kwargs):
        categories = []
        result, error = {'categories': categories}, False
        categories_by_full_name = Category.get_categories_info()['by_full_name']
        # TODO: better drf-based serialization?
        for (name, category) in sorted(categories_by_full_name.items(), key=lambda x: x[0]):
            categories.append(self._serialize_category(category))
        return result, error

    def _parse_dt_str(self, dt_str, formats=['%Y-%m-%d %H:%M:%S.%f', '%Y-%m-%d %H:%M:%S']):
        """
        yet just one supported fmt:
        'dt_from': '2025-04-01 00:00:00.000001+00:00'
        or
        'dt_from': '2025-04-01 00:00:00.000001Z'
        """
        if not dt_str:
            return None
        if '+' in dt_str:
            # assume UTC timezone always
            dt_str = dt_str.rsplit('+', 1)[0]
        elif 'Z' in dt_str:
            # assume UTC timezone always
            dt_str = dt_str.rsplit('Z', 1)[0]
        return parse_dt(dt_str, fmts=formats, as_utc=True)

    def _strf_dt(self, dt):
        if not dt:
            return ''
        return str(dt)

    def _serialize_product(self, product, **kwargs):
        return {
            'id': product.id,
            'vendor_id': product.vendor_id,
            'name': product.name,
            'is_deleted': product.is_deleted,
            'dt_created': self._strf_dt(product.dt_created),
            'dt_updated': self._strf_dt(product.dt_updated),
        }

    features_default = {
        'active_only': False,
        'with_brand': True,
        'with_vendor_colors': True,
        'with_vc': True,
        'with_pictures': True,

        'with_material': False,
        'with_tags': False,
    }

    def _get_products_qs(self, request, features=None, *args, **kwargs):
        products_qs = Product.objects.all().order_by('id')
        if features['vendors_ids']:
            products_qs = products_qs.filter(vendor_id__in=features['vendors_ids'])

        if features['dt_from']:
            products_qs = products_qs.filter(
                Q(dt_updated__gt=features['dt_from']) | Q(dt_created__gt=features['dt_from'])
            )
        # else:
        #     if 'active_only' not in featuresfilters and not 'active_only' not in features_from_request:
        #         # если dt_from НЕ передается, то мы скачиваем лишь ТОЛЬКО активные продукты!
        #         features['active_only'] = True

        if features['active_only']:
            products_qs = products_qs.exclude(is_deleted=True)

        return products_qs

    def _do_export_products(self, request, *args, **kwargs):
        products_datas = []
        result, error = {'products': products_datas}, False

        # default filters options
        vendors_ids = []
        dt_from = None

        data = request.data
        page_size = request.GET.get('page_size') or data.get('page_size') or self.page_size_products
        page = request.GET.get('page') or data.get('page')

        features = data.get('features', {})
        if not features:
            features = self.features_default
        else:
            for k, v in self.features_default.items():
                if k not in features:
                    features[k] = v

        filters = data.get('filters') or {}
        if filters:
            dt_from = self._parse_dt_str(filters.get('dt_from'))
            if 'active_only' in filters:
                features['active_only'] = filters['active_only']  # active_only reset to False possible from filters
            if 'vendors_ids' in filters:
                vendors_ids = filters['vendors_ids']
            elif 'vendor_id' in filters:
                vendors_ids = [filters['vendor_id']]

        features['vendors_ids'] = vendors_ids
        features['dt_from'] = dt_from

        products_qs = self._get_products_qs(request, features=features, *args, **kwargs)

        paginator = Paginator(products_qs, page_size)
        page_obj = paginator.get_page(page)

        result['num_pages'] = paginator.num_pages
        result['current_page'] = page or 0
        result['total_products_count'] = paginator.count
        result['page_size'] = page_size

        stats = defaultdict(int)

        for product in page_obj.object_list:
            product_data = {
                'dt_created': str_dt(product.dt_created),  #  str(product.dt_created).rsplit('+', 1)[0],
                'dt_updated': str_dt(product.dt_updated),  # str(product.dt_updated or '').rsplit('+', 1)[0],
                'is_deleted': product.is_deleted,

                'id': product.id,
                'name': product.name,
                'vendor_id': product.vendor_id,
            }
            product_data.update(product.data)
            products_datas.append(product_data)

        result['stats'] = dict(stats)
        result['next_page'] = page_obj.has_next() and page_obj.number+1
        result['indexes'] = [page_obj.start_index(), page_obj.end_index()]

        if result['next_page']:
            result['next_page_url'] = f'/api/v3/export/products/?page={result["next_page"]}'
            if page_size != self.page_size_products:
                result['next_page_url'] += f'&page_size={page_size}'

        return result, error

    def _do_export(self, request, export_func=None, *args, **kwargs):

        if getattr(request, 'authentication_error', False):
            result_status, result_error = status.HTTP_403_FORBIDDEN, request.authentication_error
            return self.response(
                error=result_error,
                result_status=result_status,
                **{}
            )

        assert export_func

        result, result_status = {}, status.HTTP_200_OK
        if getattr(request, 'authentication_error', False):
            result_status, result_error = status.HTTP_403_FORBIDDEN, request.authentication_error
        else:
            result, result_error = export_func(request, *args, **kwargs)

        if result:
            if 'status' in result:
                result_status = result.pop('status')
            if 'error' in result:
                result_error = result.pop('error') or result_error

        return self.response(
            error=result_error,
            result_status=result_status,
            **result
        )
