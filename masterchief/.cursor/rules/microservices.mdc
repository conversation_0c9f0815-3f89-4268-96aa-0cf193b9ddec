---
description: 
globs: 
alwaysApply: false
---
1. For project microservices which is current, always use virtual env python located here:
/home/<USER>/miniconda3/envs/microservices/bin/python

2. this env is used actually, conda-based
# wmi is an alias to qucik get to conda env in terminal
odyssey@Itaka:~$ wmi
(microservices) odyssey@Itaka:~/Projects/microservices$ which python
/home/<USER>/miniconda3/envs/microservices/bin/python
(microservices) odyssey@Itaka:~/Projects/microservices$ 