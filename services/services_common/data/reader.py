import sys
import os.path
from config import settings

# /home/<USER>/Projects/microservices/masterchief
# TODO: add special BaseApiClient for microservices/services?
# atm, just reusing existing api client code
sys.path.insert(0, os.path.join(settings.BASE_DIR, 'masterchief'))
from service_api_client.export_service_api_client import ServiceApiClient as BaseApiClient

MASTERCHIEF_HOST = getattr(settings, 'MASTERCHIEF_HOST', 'http://127.0.0.1:8000')
MASTERCHIEF_API_ENDPOINT = MASTERCHIEF_HOST + '/api/v3/export/'


class DataReader(BaseApiClient):
    endpoint = MASTERCHIEF_API_ENDPOINT
