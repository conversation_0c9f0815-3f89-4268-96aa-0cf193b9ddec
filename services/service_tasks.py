#  plan: certain tasks, or Consumer (sub)class to be service-dependent jobs
from config import settings, huey

import sys
sys.path.insert(0, settings.BASE_DIR)

from services.services_common.data.reader import DataReader
from services.consumer import Consumer


@huey.task()
def count_beans(num):
    print('-- counted %s beans --' % num)


@huey.task()
def update_vendors_from_masterchief():
    Consumer().update_vendors()


@huey.task()
def update_products_from_masterchief():
    Consumer().update_products()
