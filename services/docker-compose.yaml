version: '3.8'

services:
  consumer-web:
    container_name: 'consumer-web'
    build: .
    working_dir: /app/services
    command: python main.py
    volumes:
      - .:/app/services
#    environment:
#      - MASTERCHIEF_HOST=http://host.docker.internal:8000
#      - REDIS_URI=redis://consumer-redis:6379/0
    depends_on:
      - consumer-redis
    restart: unless-stopped

  consumer-redis:
    container_name: 'consumer-redis'
    image: redis:alpine
#    ports:
#      - "6390:6390"
    restart: unless-stopped
    command: redis-server
