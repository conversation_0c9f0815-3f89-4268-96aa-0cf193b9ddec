import huey

from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import Field, HttpUrl, Extra, DirectoryPath

# from dotenv import load_dotenv

from .paths import PROJECT_DIR, BASE_DIR
# from .queue_backends import huey, REDIS_URI

ENV_FILE_PATH = PROJECT_DIR / ".env"
# load_dotenv(dotenv_path=ENV_FILE_PATH)


class Settings(BaseSettings):
    """
    Base settings class that loads from .env file

    https://docs.pydantic.dev/latest/concepts/pydantic_settings/#usage
    https://docs.pydantic.dev/1.10/usage/settings/#__tabbed_1_2
    https://stackoverflow.com/a/75744270
    https://fastapi.tiangolo.com/ru/advanced/settings/#the-main-app-file
    """
    MASTERCHIEF_HOST: Optional[str] = 'http://127.0.0.1:8000'  # Optional[HttpUrl]
    REDIS_URI: Optional[str] = 'redis://consumer-redis:6379/0'  # REDIS_URI

    PROJECT_DIR: Optional[str] = str(PROJECT_DIR)
    BASE_DIR: Optional[str] = str(BASE_DIR)

    # seems like this is redundant
    class Config:
        extra = Extra.allow  # "forbid"  # remove
        env_file = ENV_FILE_PATH
        env_file_encoding = "utf-8"
        case_sensitive = True

# Create a global settings instance
settings = Settings()

huey = huey.RedisHuey(url=settings.REDIS_URI)
