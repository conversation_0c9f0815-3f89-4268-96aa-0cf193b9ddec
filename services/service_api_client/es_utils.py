import datetime


DEFAULT_DATETIME_FORMAT = '%Y-%m-%d %H:%M:%S'


def get_utc_now():
    return datetime.datetime.now().astimezone(datetime.timezone.utc)



def parse_dt(dt_str, fmts=['%Y-%m-%d', '%Y-%m-%d %H:%M', DEFAULT_DATETIME_FORMAT], default=None, as_utc=False):
    if not dt_str:
        return
    dt_str = dt_str.strip()
    for fmt in fmts:
        try:
            parsed = datetime.datetime.strptime(dt_str, fmt)
            if as_utc:
                # parsed = parsed.replace(tzinfo=pytz.UTC)
                # parsed = pytz.UTC.localize(parsed)
                parsed = parsed.astimezone(datetime.timezone.utc)
            return parsed
        except:
            continue
    return default


def str_dt(dt):
    return  str(dt or '').rsplit('+', 1)[0]
