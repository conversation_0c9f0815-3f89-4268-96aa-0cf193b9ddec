from pprint import pprint

from services_common.data.reader import DataReader
from services_common import logger

# from common.utils import get_now, get_utc_now, str_dt
from service_api_client.es_utils import get_utc_now, str_dt

class Consumer:
    @property
    def reader(self, **kwargs):
        return DataReader(**kwargs)

    @property
    def service_name(self):
        return 'base_service'

    def on_last_chunk_data(self):
        return {
            'service_name': self.service_name,
            'dt_latest': str_dt(get_utc_now())
        }

    def update_products(self, **kwargs):
        filters = {}
        dt_latest = self.reader.get_dt_latest(service_name=self.service_name)
        if dt_latest:
            filters['dt_from'] = dt_latest

        chunked_data = self.reader.get_products_chunked_datas(
            get_raw_response=True,
            filters=filters,
            # payload_addon={'service_name': self.service_name},
            on_last_chunk=self.on_last_chunk_data,
        )

        logger.info(f'Total products to sync on: {chunked_data["count"]}')
        for i, products_chunk in chunked_data['chunks']:
            self.handle_products_chunk(products_chunk)
            logger.info(f'Processed {i} of {chunked_data["total_pages"]} pages')
        logger.info(f'Finished update_products')

    def handle_products_chunk(self, products):
        print(len(products or []))

    def update_vendors(self, **kwargs):
        vendors_data = self.reader.get_vendors()
        pprint(
            vendors_data
        )
