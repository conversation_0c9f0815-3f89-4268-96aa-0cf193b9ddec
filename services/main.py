from config import settings
from services_common.data.reader import DataReader
from consumer import Consumer
from service_tasks import count_beans, update_vendors_from_masterchief, update_products_from_masterchief


def main():
    # Consumer().update_vendors()
    Consumer().update_products()

def run_count_beans():
    beans = input('How many beans? ')
    count_beans(int(beans))
    print('Enqueued job to count %s beans' % beans)


def update():
    print('update_vendors_from_masterchief started...')
    update_vendors_from_masterchief()
    print('update_products_from_masterchief started...')
    update_products_from_masterchief()


if __name__ == "__main__":
    # main()
    # run_count_beans()
    update()
