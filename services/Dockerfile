FROM python:3.11-slim

WORKDIR /app

# Install system dependencies including PostgreSQL development files
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    procps \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install pip==25.1.1
RUN pip install --no-cache-dir -r requirements.txt

# common between masterchief and services? TODO: consider on separate
# COPY ../masterchief/service_api_client .

# Copy project files
# Note: that should be specific per service?
COPY . .


# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# Add a healthcheck to verify the application is running
#HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
#  CMD curl -f http://localhost:8000/ || exit 1

# Set entrypoint
COPY service_entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

# queue and task consumers should be run in enprypoint, ATM huey
ENTRYPOINT ["/entrypoint.sh"]

## Command to run the application using Gun<PERSON> with Uvicorn workers for ASGI
#CMD ["gunicorn", "<service fast api???>.asgi:application", "--bind", "0.0.0.0:8000", "--workers", "4", "--worker-class", "uvicorn.workers.UvicornWorker"]