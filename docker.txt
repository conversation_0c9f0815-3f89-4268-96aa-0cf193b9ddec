# MasterChief django based web app #########################################################
$ docker-compose --version
> Docker Compose version v2.24.6

(microservices) odyssey@Itaka:~/Projects/microservices/masterchief$ ls -la | grep Do
-rw-rw-r-- 1 <USER> <GROUP>  946 мая  5 12:06 Dockerfile

# to build a container, use:
$ docker-compose up --build

# in case of error
> Cannot connect to the Docker daemon at unix:///var/run/docker.sock. Is the docker daemon running?
# just run service
$ sudo service docker start
# and build again
$ docker-compose up --build


# quick restart
docker-compose down && docker-compose build && docker-compose up

# Services web app #########################################################
(atm just celery + consumer)